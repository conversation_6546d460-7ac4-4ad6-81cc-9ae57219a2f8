using GeoEngine;
using GeoEngine.API;
using GeoEngine.Core;
using GeoEngine.Mode;
using SignalAnalyzer.Common;
using SignalAnalyzer.Functions;
using SignalAnalyzer.Objects;
using SignalAnalyzer.Utilities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Remoting.Channels;
using System.Runtime.Remoting.Channels.Tcp;
using System.Text;
using System.Windows.Forms;

namespace SignalAnalyzer
{
    public partial class DataAnalysis : Form
    {
        public DataAnalysis()
        {
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer 
                | ControlStyles.ResizeRedraw 
                | ControlStyles.AllPaintingInWmPaint, 
                true);
            this.UpdateStyles();

            InitializeComponent();

            DoubleBuffered(this.dataGridView1, true);
            DoubleBuffered(this.dgvResult, true);

            // 当前时间
            var now = DateTime.Now;
            this.timeStart.DateTimeValue = now.Date;
            this.timeEnd.DateTimeValue = now.Date.AddDays(1).AddMilliseconds(-1);

            this.timeUp.DateTimeValue = now.Date;
            this.timeDown.DateTimeValue = now.Date.AddDays(1).AddMilliseconds(-1);

            this.SetTimerText(now.ToString());

            // 读取必要配置信息
            IniHelper.Path = AppContext.AppPath + @"config/GeneralConfig.ini";
            //this.cbUseUTC.Checked = Convert.ToBoolean(IniHelper.GetIniKey("Time", "Utc"));

            AppContext.SatEleMin = Convert.ToDecimal(IniHelper.GetIniKey("Analysis Settings", "MinEle"));
            AppContext.SatEleMax = Convert.ToDecimal(IniHelper.GetIniKey("Analysis Settings", "MaxEle"));

            AppContext.UseIPM = Convert.ToBoolean(IniHelper.GetIniKey("Analysis Settings", "UseIPM"));
            AppContext.IsCompact = Convert.ToBoolean(IniHelper.GetIniKey("Analysis Settings", "Compact"));
            
            string tlePath = IniHelper.GetIniKey("File Path", "TLEPath");
            if (!string.IsNullOrWhiteSpace(tlePath))
            {
                AppContext.TLEPath = tlePath;
            }
            string dbPath = IniHelper.GetIniKey("File Path", "DBPath");
            if (!string.IsNullOrWhiteSpace(dbPath))
            {
                AppContext.DBPath = dbPath;
            }

            // 读取配置参数
            IniHelper.Path = AppContext.AppPath + @"config/config.ini";
            AppContext.TimeSpec = Convert.ToDouble(IniHelper.GetIniKey("AnalysisParm", "TIME_S"));
            AppContext.PRISpec = Convert.ToDouble(IniHelper.GetIniKey("AnalysisParm", "PRI_S"));
            AppContext.ELRE = Convert.ToDouble(IniHelper.GetIniKey("AnalysisParm", "EL_RE"));
            AppContext.AZRE = Convert.ToDouble(IniHelper.GetIniKey("AnalysisParm", "AZ_RE"));

            AsyncTask.InitUIContext();
        }

        private readonly object lockSync = new object();

        private SiteEquator CurrentSite;

        // dgvResult 拖拽多选相关变量
        private bool isDragging = false;
        private int dragStartRowIndex = -1;
        private int dragCurrentRowIndex = -1;
        /// <summary>
        /// 激活窗口时的一些操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DataAnalysis_Activated(object sender, EventArgs e)
        {
            lock (lockSync)
            {
                if (ExcelExport.StartStatus && !IsExcelOut)
                {
                    ExcelExport.Close();
                }

                if (CurrentSite == null)
                {
                    CurrentSite = new SiteEquator();
                }
                if (AppContext.SiteEquator == null)
                {
                    string sqlSat = "SELECT SiteName,SiteLatitude,SiteLongitude,SiteAltitude FROM DB_K_SITE WHERE SiteSelect=1";
                    var site = SQLiteHelper.ExecuteDataReader(sqlSat);
                    AppContext.SiteEquator = new SiteEquator();
                    while (site.Read())
                    {
                        CurrentSite.SiteName = site.GetString(0);
                        CurrentSite.SiteLatitude = site.GetDouble(1);
                        CurrentSite.SiteLongitude = site.GetDouble(2);
                        CurrentSite.SiteAltitude = site.GetDouble(3);
                    }
                    DataConvert.UpdateValue(CurrentSite, ref AppContext.SiteEquator);
                }
                else if (CurrentSite.SiteName == AppContext.SiteEquator.SiteName
                    && CurrentSite.SiteLatitude == AppContext.SiteEquator.SiteLatitude
                    && CurrentSite.SiteLongitude == AppContext.SiteEquator.SiteLongitude
                    && CurrentSite.SiteAltitude == AppContext.SiteEquator.SiteAltitude)
                {
                    // 防止频繁刷新
                    return;
                }
                else
                {
                    // 更新CurrentSite
                    DataConvert.UpdateValue(AppContext.SiteEquator, ref CurrentSite);
                }

                if (string.IsNullOrWhiteSpace(AppContext.SiteEquator.SiteName))
                {
                    this.tbSiteInfo.Text = "未配置";
                }
                else
                {
                    this.tbSiteInfo.Text = $"{AppContext.SiteEquator.SiteName}：{AppContext.SiteEquator.SiteLongitude},{AppContext.SiteEquator.SiteLatitude}，{AppContext.SiteEquator.SiteAltitude}m";
                }
            }
        }

        /// <summary>
        /// 初始化一个计时器用于时钟显示
        /// </summary>
        private System.Timers.Timer timer = new System.Timers.Timer(1000);
        private void SetTimerText(string timeValue)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new MethodInvoker(delegate
                {
                    this.toolSynTime.Text = timeValue;
                }));
            }
            else
            {
                this.toolSynTime.Text = timeValue;
            }
        }

        /// <summary>
        /// 窗口关闭时的一些操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Main_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (stkSimulatorProcess != null && !stkSimulatorProcess.HasExited)
            {
                var result = MessageBox.Show("STK模拟器未关闭，是否关闭？", "警告", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    stkSimulatorProcess.CloseMainWindow();
                    stkSimulatorProcess.Close();
                    stkSimulatorProcess = null;
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }

            timer.Stop();
            timer.Elapsed -= Timer_Elapsed;
            
            timer.Close();
            timer.Dispose();

            if (this.Owner != null && !this.Owner.IsDisposed)
            {
                this.Owner.Show();
            }
        }

        /// <summary>
        /// 窗体加载时的工作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Main_Load(object sender, EventArgs e)
        {
            MapNewSize();
            this.mapPanel.BackgroundImage = ResourcesRegulator.GetBitmap("GDMap_05_A.jpg", this.mapPanel.ClientRectangle.Size);

            timer.Elapsed += Timer_Elapsed;
            timer.AutoReset = true;
            timer.Start();

        }

        /// <summary>
        /// 时钟周期变化显示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Timer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            this.SetTimerText(e.SignalTime.ToString());
        }

        /// <summary>
        /// 页面大小变化时的工作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tabPage1_Resize(object sender, EventArgs e)
        {
            if (this.WindowState != FormWindowState.Minimized)
            {
                MapNewSize();
                DrawSite();
            }
        }


        //******** 定义代理方法，解决多线程环境中跨线程改写 ui 控件属性，开始 ********   

        //定义设置一个文本的委托方法（字符串）
        internal void SetProgressVal(int val)
        {
            AsyncTask.SwitchToUI(() =>
            {
                if (this.InvokeRequired)
                {
                    this.BeginInvoke(new MethodInvoker(delegate
                    {
                        this.progressBarDisplay.Value = val;
                        this.percentageValue.Text = $"{ val }%";
                    }));
                }
                else
                {
                    this.progressBarDisplay.Value = val;
                    this.percentageValue.Text = $"{ val }%";
                }
                Application.DoEvents();
                System.Threading.Thread.Sleep(100);
            });
        }
        //UpdateProgressVal

        /// <summary>
        /// 输出日志
        /// </summary>
        /// <param name="log"></param>
        internal void SetLogText(string log)
        {
            string temp = DateTime.Now.ToString() + "|" + log;
            if (this.appLog.InvokeRequired)
            {
                this.appLog.BeginInvoke(new MethodInvoker(delegate
                {
                    LogText(temp);
                }));
            }
            else
            {
                LogText(temp);
            }
        }

        private void LogText(string log)
        {
            this.appLog.AppendText($"{log}\r\n");
            if (this.appLog.Lines.Length > 100)
            {
                this.appLog.Lines = this.appLog.Lines.Skip(80).ToArray();
            }
        }

        //******** 定义代理方法，解决多线程环境中跨线程改写 ui 控件属性  结束 ********   




        #region 界面跳转打开及NotifyMenu的操作


        private RadarEdits radarEditForm;
        private OrbitPredictor.OrbitMain orbitMain;
        private Process stkSimulatorProcess;

        private void OpenStkEngine()
        {
            lock (lockSync)
            {
                if (stkSimulatorProcess == null || stkSimulatorProcess.HasExited)
                {
                    Process[] processes = Process.GetProcessesByName("STKSimulator");
                    if (processes.Length > 0)
                    {
                        stkSimulatorProcess = processes[0];
                        stkSimulatorProcess.StartInfo.UseShellExecute = false;
                        stkSimulatorProcess.StartInfo.RedirectStandardInput = true;
                    }
                    else
                    {
                        stkSimulatorProcess = new Process();
                        ProcessStartInfo startInfo = new ProcessStartInfo();
                        startInfo.FileName = @"plugin\STKSimulator\STKSimulator.exe";
                        //startInfo.Arguments = "/a";
                        startInfo.UseShellExecute = false;
                        startInfo.RedirectStandardInput = true;
                        stkSimulatorProcess.StartInfo = startInfo;
                        stkSimulatorProcess.Start();
                    }
                }
                if (!stkSimulatorProcess.EnableRaisingEvents)
                {
                    stkSimulatorProcess.EnableRaisingEvents = true;
                    stkSimulatorProcess.Exited += (obj, ea) =>
                    {
                        stkSimulatorProcess.Close();
                        stkSimulatorProcess = null;
                        var clientChannel = (TcpChannel)ChannelServices.GetChannel("tcpClient");
                        if (clientChannel != null)
                        {
                            clientChannel.StopListening(null);
                            ChannelServices.UnregisterChannel(clientChannel);
                        }
                    };
                }
                Program.HandleRunningInstance(stkSimulatorProcess); //激活窗口并显示

                var channelCount = ChannelServices.RegisteredChannels.Count();
                if (channelCount == 0)
                {
                    BinaryServerFormatterSinkProvider serverProvider = new BinaryServerFormatterSinkProvider();
                    BinaryClientFormatterSinkProvider clientProvider = new BinaryClientFormatterSinkProvider();
                    serverProvider.TypeFilterLevel = System.Runtime.Serialization.Formatters.TypeFilterLevel.Full;
                    IDictionary iDict = new Hashtable();
                    iDict["name"] = "tcpClient";
                    iDict["port"] = "0";
                    TcpChannel clientChannel = new TcpChannel(iDict, clientProvider, serverProvider);
                    ChannelServices.RegisterChannel(clientChannel, true); //ContectSTKEngine
                }
                //var rCommands = (IRemoteContect.Message)Activator.GetObject(typeof(IRemoteContect.Message), "tcp://localhost:52580/RemoteCommands");
                //rCommands.SendMessage("Suff ");
                stkSimulatorProcess.WaitForInputIdle();
                var wait = Program.WaitForHandleShownInstance(stkSimulatorProcess);
                if (!wait)
                {
                    MessageBox.Show("等待超时，STK模拟器未正确启动", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }


        /// <summary>
        /// 打开雷达参数编辑界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmParamEdit_Click(object sender, EventArgs e)
        {
            if (radarEditForm == null || radarEditForm.IsDisposed)
            {
                radarEditForm = new RadarEdits();
                radarEditForm.Show(this);
            }
            else
            {
                radarEditForm.WindowState = FormWindowState.Normal;
                radarEditForm.Activate();
            }
        }

        /// <summary>
        /// 打开卫星轨道预报界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmOpenOrbit_Click(object sender, EventArgs e)
        {
            if (orbitMain == null || orbitMain.IsDisposed)
            {
                orbitMain = new OrbitPredictor.OrbitMain();
                orbitMain.Show(this);
            }
            else
            {
                orbitMain.WindowState = FormWindowState.Normal;
                orbitMain.Activate();
            }
        }

        /// <summary>
        /// 打开系统设置界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmSystemSetting_Click(object sender, EventArgs e)
        {
            var sysSet = new SystemSetting();
            sysSet.ShowDialog();
        }

        /// <summary>
        /// 打开STK轨道仿真界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmOpenSTKTool_Click(object sender, EventArgs e)
        {
            OpenStkEngine();
        }
        
        private void appExit_Click(object sender, EventArgs e)
        {
            Environment.Exit(0);
        }

        private void showMain_Click(object sender, EventArgs e)
        {
            this.Show();
            this.WindowState = FormWindowState.Normal;
            this.Activate();
        }

        #endregion



        #region 数据表格相关操作实现

        /// <summary>
        /// 给DataGridView启用双缓冲
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="setting"></param>
        public new void DoubleBuffered(DataGridView dgv, bool setting)
        {
            Type dgvType = dgv.GetType();
            PropertyInfo pi = dgvType.GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic);
            pi.SetValue(dgv, setting, null);
        }

        /// <summary>
        /// 表格自增序列号
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dataGridView1_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
        {
            System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle(e.RowBounds.Location.X, 
                                                                            e.RowBounds.Location.Y, 
                                                                            this.dataGridView1.RowHeadersWidth - 4, 
                                                                            e.RowBounds.Height);
            TextRenderer.DrawText(e.Graphics, 
                        (e.RowIndex + 1).ToString(), 
                        this.dataGridView1.RowHeadersDefaultCellStyle.Font, 
                        rectangle, 
                        this.dataGridView1.RowHeadersDefaultCellStyle.ForeColor, 
                        TextFormatFlags.VerticalCenter | TextFormatFlags.Right);
        }

        /// <summary>
        /// 鼠标按下选中行
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dataGridView1_CellMouseDown(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right && e.RowIndex > -1 && e.ColumnIndex > -1)
            {
                var dgv = (sender as DataGridView);

                // 检查点击的行是否已经被选中
                bool isRowSelected = dgv.Rows[e.RowIndex].Selected;

                // 如果点击的行没有被选中，则需要选中它
                if (!isRowSelected)
                {
                    // 如果没有按住Ctrl键，清除之前的选择
                    if ((Control.ModifierKeys & Keys.Control) == 0)
                    {
                        dgv.ClearSelection();
                    }
                    dgv.Rows[e.RowIndex].Selected = true;
                }

                dgv.CurrentCell = dgv.Rows[e.RowIndex].Cells[e.ColumnIndex];
            }
        }

        /// <summary>
        /// 选中单元格后的操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dataGridView1_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
        {
            // 计算平均值，只计算选中数值时
            List<double> avg = new List<double>();
            foreach (DataGridViewCell dc in this.dataGridView1.SelectedCells)
            {
                double n;
                if (!double.TryParse(Convert.ToString(dc.Value), out n))
                {
                    this.coordinateText.Text = "-";
                    avg.Clear();
                    break;
                }
                avg.Add(n);
            }
            if (avg.Count > 0)
            {
                this.coordinateText.Text = $"平均值：{ avg.Average():f2}";
            }
        }

        /// <summary>
        /// 选中多行时，统计相关参数
        /// </summary>
        private bool table1RowsSelected = false;
        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            var rows = this.dataGridView1.SelectedRows;
            if (rows.Count > 0 && dataState > 2)
            {
                table1RowsSelected = true;

                List<DateTime> times = new List<DateTime>();
                List<double> freq = new List<double>();
                List<double> pri = new List<double>();
                List<double> pw = new List<double>();
                List<double> pm = new List<double>();
                List<double> pa = new List<double>();
                foreach (DataGridViewRow row in rows)
                {
                    times.Add((DateTime)row.Cells[4].Value);
                    freq.Add((double)row.Cells[8].Value);
                    freq.Add((double)row.Cells[9].Value);
                    pm.Add((double)row.Cells[10].Value - (double)row.Cells[11].Value);
                    pri.Add((double)row.Cells[13].Value);
                    pri.Add((double)row.Cells[14].Value);
                    pw.Add((double)row.Cells[16].Value);
                    pw.Add((double)row.Cells[17].Value);
                    pa.Add(Convert.ToDouble(row.Cells[21].Value));
                }
                var paPrint = SignalSelectionImpl.NormalizeToRange(pa, 0, pa.Max() * 1.2, 35, 90);

                this.descriptionText.Text = $"幅度：{paPrint.Average():f2}， 中心频率：{(int)((freq.Min() + freq.Max()) / 2)}，重复周期：{ pri.Average():f2}，脉宽：{ pw.Max():f1}，脉内带宽：{ pm.Max():f0}，持续时间(s)：{ (LocalTimeChange.DateTimeToLong(times.First()) - LocalTimeChange.DateTimeToLong(times.Last())) / 1000 + 1}";
            }
            else
            {
                if (table1RowsSelected)
                {
                    this.descriptionText.Text = "欢迎使用！";
                }
                table1RowsSelected = false;
            }
        }

        /// <summary>
        /// 右键菜单打开时的操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void menuDataOption_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if (this.tabControl1.SelectedIndex == 0)
            {
                if (this.dataGridView1.ColumnCount > 0)
                {
                    if (dataState < 3)
                    {
                        //e.Cancel = true;
                        //return;
                    }
                    else
                    {
                        if (errorRsData.Count > 0)
                        {
                            this.exportErrExcel.Enabled = true;
                        }
                        else
                        {
                            this.exportErrExcel.Enabled = false;
                        }
                        this.myAnyalysis.Visible = true;
                        this.tsmOperation.Visible = true;
                        return;
                    }
                }
            }
            else if (this.tabControl1.SelectedIndex == 2)
            {
                // 数据查询页面，检查dgvResult是否有数据
                if (this.dgvResult.RowCount > 0)
                {
                    // 允许显示右键菜单，但隐藏分析相关的菜单项
                    this.myAnyalysis.Visible = false;
                    this.tsmOperation.Visible = false;
                    return;
                }
            }
            e.Cancel = true;
        }

        #endregion

        #region dgvResult 多选功能实现

        /// <summary>
        /// dgvResult 鼠标按下事件 - 处理右键菜单选择逻辑
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvResult_CellMouseDown(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right && e.RowIndex > -1 && e.ColumnIndex > -1)
            {
                var dgv = (sender as DataGridView);

                // 检查点击的行是否已经被选中
                bool isRowSelected = dgv.Rows[e.RowIndex].Selected;

                // 如果点击的行没有被选中，则需要选中它
                if (!isRowSelected)
                {
                    // 如果没有按住Ctrl键，清除之前的选择
                    if ((Control.ModifierKeys & Keys.Control) == 0)
                    {
                        dgv.ClearSelection();
                    }
                    dgv.Rows[e.RowIndex].Selected = true;
                }

                dgv.CurrentCell = dgv.Rows[e.RowIndex].Cells[e.ColumnIndex];
            }
        }

        /// <summary>
        /// dgvResult 鼠标释放事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvResult_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
        {
            // 可以在这里添加选中后的统计信息显示
            var dgv = (sender as DataGridView);
            if (dgv.SelectedRows.Count > 0)
            {
                this.coordinateText.Text = $"已选择 {dgv.SelectedRows.Count} 行数据";
            }
        }

        /// <summary>
        /// dgvResult 鼠标按下事件 - 处理拖拽多选开始
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvResult_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                var dgv = (sender as DataGridView);
                var hitTest = dgv.HitTest(e.X, e.Y);

                // 检查是否点击在行头区域
                if (hitTest.Type == DataGridViewHitTestType.RowHeader && hitTest.RowIndex >= 0)
                {
                    isDragging = true;
                    dragStartRowIndex = hitTest.RowIndex;
                    dragCurrentRowIndex = hitTest.RowIndex;

                    // 如果没有按住Ctrl键，清除之前的选择
                    if ((Control.ModifierKeys & Keys.Control) == 0)
                    {
                        dgv.ClearSelection();
                    }

                    // 选中起始行
                    dgv.Rows[hitTest.RowIndex].Selected = true;
                    dgv.CurrentCell = dgv.Rows[hitTest.RowIndex].Cells[0];
                }
            }
        }

        /// <summary>
        /// dgvResult 鼠标移动事件 - 处理拖拽多选过程
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvResult_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.Button == MouseButtons.Left)
            {
                var dgv = (sender as DataGridView);
                var hitTest = dgv.HitTest(e.X, e.Y);

                // 检查是否在有效的行上
                if (hitTest.RowIndex >= 0 && hitTest.RowIndex != dragCurrentRowIndex)
                {
                    dragCurrentRowIndex = hitTest.RowIndex;

                    // 计算选择范围
                    int startIndex = Math.Min(dragStartRowIndex, dragCurrentRowIndex);
                    int endIndex = Math.Max(dragStartRowIndex, dragCurrentRowIndex);

                    // 如果没有按住Ctrl键，先清除所有选择
                    if ((Control.ModifierKeys & Keys.Control) == 0)
                    {
                        dgv.ClearSelection();
                    }

                    // 选择范围内的所有行
                    for (int i = startIndex; i <= endIndex; i++)
                    {
                        if (i < dgv.Rows.Count)
                        {
                            dgv.Rows[i].Selected = true;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// dgvResult 鼠标释放事件 - 处理拖拽多选结束
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvResult_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && isDragging)
            {
                isDragging = false;
                dragStartRowIndex = -1;
                dragCurrentRowIndex = -1;

                // 更新状态栏信息
                var dgv = (sender as DataGridView);
                if (dgv.SelectedRows.Count > 0)
                {
                    this.coordinateText.Text = $"已选择 {dgv.SelectedRows.Count} 行数据";
                }
            }
        }

        /// <summary>
        /// dgvResult 选择变化事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvResult_SelectionChanged(object sender, EventArgs e)
        {
            var dgv = (sender as DataGridView);
            if (dgv.SelectedRows.Count > 0)
            {
                this.coordinateText.Text = $"已选择 {dgv.SelectedRows.Count} 行数据";
            }
            else
            {
                this.coordinateText.Text = "0,0";
            }
        }

        #endregion



        #region 地图相关

        private int xPos;
        private int yPos;
        private bool MoveFlag;
        private void mapPanel_MouseDown(object sender, MouseEventArgs e)
        {
            var pb = sender as Control;
            pb.Cursor = Cursors.SizeAll;
            MoveFlag = true; // 已经按下
            xPos = e.X; // 当前x坐标
            yPos = e.Y; // 当前y坐标
        }

        private void mapPanel_MouseUp(object sender, MouseEventArgs e)
        {
            var pb = sender as Control;
            pb.Cursor = Cursors.Cross;
            MoveFlag = false;
        }

        private void mapPanel_MouseMove(object sender, MouseEventArgs e)
        {
            if (MoveFlag)
            {
                // 鼠标按下移动
                // 框选区域放大
            }
            else
            {
                var pb = sender as Control;
                MapPoint mp = Translator.ScreenToMercator(e.X, e.Y, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
                MapPoint newMp = Translator.MercatorToLonLat(mp);
                this.coordinateText.Text = $"{ newMp.X:f6},{ newMp.Y:f6}";
            }
        }

        private void mapPanel_Paint(object sender, PaintEventArgs e)
        {
            var pb = sender as Control;
            //DrawGrids(pb, e);
            Brush b = new SolidBrush(Color.Red);
            MapPoint mp = new MapPoint();
            mp.X = AppContext.SiteEquator.SiteLongitude;
            mp.Y = AppContext.SiteEquator.SiteLatitude;
            MapPoint mp2 = Translator.LonLatToMercator(mp);
            //double x = DataConvert.LongitudeToX(mp2.X, pb.ClientRectangle.Width);
            //double y = DataConvert.LatitudeToY(mp2.Y, pb.ClientRectangle.Height);
            MapPoint x_y = Translator.MercatorToScreen(mp2, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
            e.Graphics.FillEllipse(b, (float)(x_y.X - 5), (float)(x_y.Y - 5), 10, 10);
        }

        /// <summary>
        /// 画经纬网格
        /// </summary>
        /// <param name="c"></param>
        /// <param name="e"></param>
        private void DrawGrids(Control c, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            Pen myPen = Pens.White;
            for (int i = 0; i < c.ClientRectangle.Width; i++)
            {
                g.DrawLine(myPen, new Point(i, 0), new Point(i, c.ClientRectangle.Bottom));
                i += (int)(c.ClientRectangle.Width / 360.0 * 30);
            }

            for (int j = 0; j < c.ClientRectangle.Height; j++)
            {
                g.DrawLine(myPen, new Point(0, j), new Point(c.ClientRectangle.Right, j));
                j += (int)(c.ClientRectangle.Height / 180.0 * 30);
            }
        }

        private void MapNewSize()
        {
            Size newSize = new Size((int)(this.tabPage1.Height * 1.5), this.tabPage1.Height);
            this.mapPanel.Size = newSize;
            this.mapPanel.Location = new Point(0);
            //this.mapPanel.Size = newSize;
        }



        #endregion

                

        #region  信号分选
        
        /// <summary>
        /// 选择的TLE名字
        /// </summary>
        private List<Tle> selectTle = new List<Tle>();
        /// <summary>
        /// TLE转成的卫星模型
        /// </summary>
        List<Orbit> orbitList = null;
        /// <summary>
        /// 加载的TLE文件路径
        /// </summary>
        private string tleFilePath;
        
        /// <summary>
        /// 数据用于显示在datagridview上的状态
        /// 1信号数据读取完毕，2卫星信息及特征模板读取完毕，3分选信号数据完毕，4合批数据处理完毕，5手动处理了合批数据，6正在处理异常数据
        /// </summary>
        private uint dataState = 0;
        /// <summary>
        /// 读取的所有信号数据 dataState = 1
        /// </summary>
        private static List<RadarSignal> signalList;
        /// <summary>
        /// 卫星(编号|名称|国家）信息及特征模板 dataState = 2
        /// </summary>
        private List<SatelliteInfo> satDiction;

        /// <summary>
        /// 观测位置
        /// </summary>
        private Site siteEquator;
        /// <summary>
        /// 载入的数据类型
        /// </summary>
        private DataFactory.DBSource dbSource;
        /// <summary>
        /// 分选后匹配的每一组数据 dataState = 3|4|5
        /// </summary>
        private Dictionary<SatelliteCast, List<RadarSignal>> groupSignalList = new Dictionary<SatelliteCast, List<RadarSignal>>();
        /// <summary>
        /// 合批输出数据列表
        /// </summary>
        private List<SignalExcel> signalExport = new List<SignalExcel>();

        /// <summary>
        /// 分选数据后的异常数据 dataState = 6;
        /// </summary>
        private List<RadarSignal> errorRsData = new List<RadarSignal>();
        
        /// <summary>
        /// 载入数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOpen_Click(object sender, EventArgs e)
        {
            try
            {
                AsyncTask.PostToUI(() =>
                {
                    this.SetProgressVal(0);
                    this.SetLogText("正在载入数据");

                    var selectOk = false;

                    SelectDataType sDataType = new SelectDataType(this.tleFilePath, this.dbSource);
                    sDataType.SeletDB += (dbSource, path, tlePath, tleType, reselect) =>
                    {
                        selectOk = true;
                        if (!Path.Equals(tleFilePath, tlePath) || reselect)
                        {
                            try
                            {
                                List<Tle> allTle = default(List<Tle>);
                                if (tleType == 1)
                                {
                                    allTle = OrbitAnalysis.AnyTLEFormFile(tlePath);
                                }
                                // 其他格式类型

                                SelectTleObject stObj = new SelectTleObject();
                                stObj.StartPosition = FormStartPosition.CenterParent;
                                stObj.SetData(allTle, selectTle);
                                stObj.SeletTle += a =>
                                {
                                    selectTle.Clear();
                                    selectTle.AddRange(a);
                                    if (dataState == 1)
                                    {
                                        var startTime = this.timeStart.DateTimeValue;
                                        if (selectTle.Count > 0 && (startTime - selectTle.Select(t => t.EpochJulian.ToTime()).Min().ToLocalTime()).TotalDays > 7)
                                        {
                                            MessageBox.Show("存在超过一周的TLE轨道，卫星已经偏移，可能导致数据分选不正确，请及时更换", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                        }
                                    }
                                };
                                stObj.ShowDialog();

                                this.SetLogText("加载TLE轨道完成");
                                tleFilePath = tlePath;
                            }
                            catch //(Exception ex)
                            {
                                this.SetLogText("TLE轨道格式错误");
                                MessageBox.Show("TLE轨道格式错误", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }

                        this.dbSource = dbSource;
                        this.SetProgressVal(30);
                        double[] stn = null;
                        // 读取不同类型的数据
                        switch (dbSource)
                        {
                            case DataFactory.DBSource.RCL673_4:
                                var l4 = new DataFactory.RCL673_4(path, AppContext.IsCompact);
                                signalList = l4.ReadData();
                                //读取MDB里的坐标
                                //DB_CM_STNSTATUS StatnID, StaAtt,StaType,Longitude,Latitude,Height
                                if (signalList.Count > 0)
                                {
                                    var sign = signalList.AsParallel()
                                        .Where(s => s.SynDate.CompareTo(DateTime.Now) < 0)
                                        .OrderByDescending(s => s.SynDate);
                                    if (sign.Count() > 0)
                                    {
                                        stn = l4.ReadStn(sign.ElementAt(0).StatnID);
                                    }
                                }
                                l4.Close(AppContext.IsCompact);
                                break;
                            case DataFactory.DBSource.RCL673_4G:
                                var l4g = new DataFactory.RCL673_4G(path, AppContext.IsCompact);
                                signalList = l4g.ReadData();
                                if (signalList.Count > 0)
                                {
                                    var sign = signalList.AsParallel()

                                        .Where(s => s.SynDate.CompareTo(DateTime.Now) < 0)
                                        .OrderByDescending(s => s.SynDate);
                                    if (sign.Count() > 0)
                                    {
                                        stn = l4g.ReadStn(sign.ElementAt(0).StatnID);
                                    }
                                }
                                l4g.Close(AppContext.IsCompact);
                                break;
                            case DataFactory.DBSource.RCL673_3G:
                                var l3g = new DataFactory.RCL673_3G(path);
                                var signal3g = l3g.ReadData();

                                this.BeginInvoke(new SetSignalCollectSource(this.SignalCollectSource), new object[] { signal3g });

                                signalList = new List<RadarSignal>();
                                //foreach (var sign in signal3g)
                                //{
                                //    signalList.Add(DataConvert.Mapper<RadarSignal, RadarSignalCollect>(sign));
                                //}

                                break;
                            default:
                                signalList = new List<RadarSignal>();
                                break;
                        }
                        if (stn != null)
                        {
                            if (Math.Abs(Math.Round(this.CurrentSite.SiteLatitude, 4) - Math.Round(stn[0], 4)) > 0.0001
                                || Math.Abs(Math.Round(this.CurrentSite.SiteLongitude, 4) - Math.Round(stn[1], 4)) > 0.0001
                                || Math.Abs(Math.Round(this.CurrentSite.SiteAltitude, 4) - Math.Round(stn[2], 4)) > 15.3)
                            {
                                if (MessageBox.Show("读取到的坐标与设置的不一致，是否使用", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                                {
                                    AppContext.SiteEquator.SiteName = $"_{AppContext.SiteEquator.SiteName}";
                                    AppContext.SiteEquator.SiteLatitude = stn[0];
                                    AppContext.SiteEquator.SiteLongitude = stn[1];
                                    AppContext.SiteEquator.SiteAltitude = stn[2];
                                    this.Activate();
                                }
                            }
                        }

                        dataState = 1;

                        this.SetProgressVal(50);
                               
                        var signalGroup = signalList.ToLookup(s => s.SynDate.CompareTo(Convert.ToDateTime("2010-01-01")) > 0 && s.SynDate.CompareTo(DateTime.Now) < 0);
                        
                        // 错误数据提取     
                        errorRsData.Clear();
                        var errData = signalGroup[false].Where(s => "线性调频".Equals(s.IPCTName));
                        if (errData.Count() > 0)
                        {
                            errorRsData.AddRange(errData);
                        }

                        var signalData = signalGroup[true];
                        if (signalData.Count() > 0)
                        {
                            //var firstSignal = signalList.Find(c => c.SynDate.CompareTo(Convert.ToDateTime("2010-01-01")) > 0);  // 2010年之后的第一个数据
                            //var lastSignal = signalList.FindLast(c => c.SynDate.CompareTo(DateTime.Now) < 0);  // 今天之前的第一个数据
                            var startTime = signalData.AsParallel().Min(c => c.SynDate);
                            var endTime = signalData.AsParallel().Max(c => c.SynDate);
                            this.timeStart.DateTimeValue = startTime;
                            this.timeEnd.DateTimeValue = endTime;

                            if (selectTle.Count > 0 && (startTime - selectTle.Select(t => t.EpochJulian.ToTime()).Min().ToLocalTime()).TotalDays > 7)
                            {
                                MessageBox.Show("存在超过一周的TLE轨道，卫星已经偏移，可能导致数据分选不正确，请及时更换", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }

                            var sidCount = signalData.GroupBy(s => s.StatnID).Count();
                            if (sidCount > 1)
                            {
                                MessageBox.Show("该时间段数据检测到多站ID", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                        else
                        {
                            // 时间范围范围内没有数据
                            // 做一些事情...

                        }
                        
                        this.SetProgressVal(80);
                        this.SetLogText("数据导入完成");


                        // 加载完轨道后加载卫星数据库
                        var satList = OrbitRelatedImpl.ReadSatelliteBaseInfo();
                        var satTemplate = OrbitRelatedImpl.ReadSatelliteParam();
                        // 卫星信息合并
                        satDiction = new List<SatelliteInfo>();
                        foreach (var sat in satList)
                        {
                            var satInfo = DataConvert.Mapper<SatelliteInfo, SatelliteBase>(sat);
                            //var satParam = DataConvert.Mapper<SatelliteInfo, SatelliteParam>();
                            //var satInfo = DataConvert.UnionPropValue(satBase, satParam);
                            satInfo.ParamList = satTemplate.FindAll(s => s.RadarNo == sat.SatelliteNo);
                            satDiction.Add(satInfo);
                        }
                        dataState = 2;

                        this.SetProgressVal(100);
                        this.SetLogText("数据加载完成");
                    };
                    sDataType.ShowDialog();

                    if (!selectOk)
                    {
                        this.SetLogText("已取消");
                    }
                });
            }
            catch (Exception err)
            {
                //如果出错，显示错误信息
                this.SetLogText(err.Message);
                MessageBox.Show(err.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空表格
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnClear_Click(object sender, EventArgs e)
        {
            AsyncTask.PostToUI(() =>
            {
                this.mapPanel.BackgroundImage = ResourcesRegulator.GetBitmap("GDMap_05_A.jpg", this.mapPanel.ClientRectangle.Size);
            });

            ClearOrbit();
            ClearSignalDataAndView();
            signalList = null;

            this.SetProgressVal(0);
            this.SetLogText("表格清空");

            GC.Collect();
            GC.WaitForPendingFinalizers();
        }

        /// <summary>
        /// 清空轨道预报数据
        /// </summary>
        private void ClearOrbit()
        {
            selectTle.Clear();
            orbitList?.Clear();
            tleFilePath = null;
        }

        /// <summary>
        /// 清空载入的数据及显示
        /// </summary>
        private void ClearSignalDataAndView()
        {
            groupSignalList.Clear();
            signalExport.Clear();
            errorRsData.Clear();

            ClearDataSource();

            dataState = 0;
        }

        private void ClearDataSource()
        {
            tableIndexs = null;
            this.dataGridView1.DataSource = null;
            this.dataGridView1.Columns.Clear();
            this.dataGridView1.Refresh();
        }


        /// <summary>
        /// 开始分析
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartWork_Click(object sender, EventArgs e)
        {
            if (selectTle.Count == 0)
            {
                this.SetLogText("未加载TLE目标");
                MessageBox.Show("未加载TLE目标", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (signalList == null)
            {
                this.SetLogText("未载入数据");
                MessageBox.Show("未载入数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var timeStart = this.timeStart.DateTimeValue;
            var timeEnd = this.timeEnd.DateTimeValue;
            if ((timeEnd - timeStart).TotalDays > 7)
            {
                var result = MessageBox.Show("时间间隔超过一周，分选时间可能会很长，是否继续？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                if (result == DialogResult.No)
                {
                    return;
                }
            }

            this.SetLogText("正在分析");
            ClearSignalDataAndView();

            AsyncTask.AsyncPost(() => RunCoreWork(), (ex) => WorkCompletecallBack(ex));

            this.btnStartWork.Enabled = false;
            this.btnDetailed.Text = "3.完整数据";
            
        }

        /// <summary>
        /// 核心任务，预报目标轨道并分选目标信号
        /// </summary>
        /// <returns></returns>
        private Result RunCoreWork()
        {
            var result = new Result();
            
            try
            {
                this.SetLogText("正在预报");
                this.SetProgressVal(0);

                var lat = AppContext.SiteEquator.SiteLatitude;
                var lon = AppContext.SiteEquator.SiteLongitude;
                var alt = AppContext.SiteEquator.SiteAltitude / 1000;
                siteEquator = new Site(lat, lon, alt);

                orbitList = OrbitRelatedImpl.ForecastOrbitData(selectTle);
                this.SetProgressVal(5);

                // 周期时间
                var timeStart = this.timeStart.DateTimeValue.ToUniversalTime();
                var timeEnd = this.timeEnd.DateTimeValue.ToUniversalTime();

                decimal[] satEle = { AppContext.SatEleMin, AppContext.SatEleMax };

                // 预报轨道过境信息
                var orbitData = OrbitRelatedImpl.ForecastOrbitView(siteEquator, orbitList, timeStart, timeEnd, satEle);

                this.SetProgressVal(30);

                var satCast = new List<SatelliteCast>();
                foreach (OrbitDataView odata in orbitData)
                {
                    SatelliteCast sc = DataConvert.Mapper<SatelliteCast, OrbitDataView>(odata);
                    satCast.Add(sc);
                }

                this.SetProgressVal(40);
                
                var index = 0;
                // 信号目标匹配
                foreach (SatelliteCast sc in satCast)
                {
                    // 从信号找目标，空域匹配
                    var targetList = signalList.Where(s => {
                        bool state = false;

                        long time = LocalTimeChange.DateTimeToLong(s.SynDate);
                        // 信号时间在卫星过境时间范围内
                        if (time > sc.InTimeMills && time < sc.OutTimeMills)
                        {
                            state = true;

                            // 获取当前时间目标的方位俯仰
                            Orbit orbit = orbitList.Find(o => o.SatNoradId == sc.SatelliteNo);
                            EciTime eciSDP4 = orbit.GetPosition(s.SynDate.ToUniversalTime());
                            Topo topoLook = siteEquator.GetLookAngle(eciSDP4);

                            // 俯仰不在误差范围内
                            if (s.InterPIT < topoLook.ElevationDeg - AppContext.ELRE
                                || s.InterPIT > (topoLook.ElevationDeg + AppContext.ELRE > 90 ? 90 : topoLook.ElevationDeg + AppContext.ELRE)) // 上限90度
                            {
                                state = false;
                            }

                            // 方位不在误差范围内
                            if (s.InterAZ < topoLook.AzimuthDeg - AppContext.AZRE
                                || s.InterAZ > topoLook.AzimuthDeg + AppContext.AZRE)
                            { // 上限360度，顺时针转归0
                                if (topoLook.AzimuthDeg - AppContext.AZRE < 0)
                                {
                                    if (s.InterAZ < topoLook.AzimuthDeg - AppContext.AZRE + 360)
                                    {
                                        state = false;
                                    }
                                }
                                else if (topoLook.AzimuthDeg + AppContext.AZRE > 360)
                                {
                                    if (s.InterAZ > topoLook.AzimuthDeg + AppContext.AZRE - 360)
                                    {
                                        state = false;
                                    }
                                }
                                else
                                {
                                    state = false;
                                }
                            }

                        }
                        return state;
                    });
                    if (targetList.Count() > 0)
                    {
                        // 信号是否在目标的参数范围内，频域匹配
                        var satInfo = satDiction.Find(sat => sat.SatelliteNo == sc.SatelliteNo);
                        if (satInfo != null)
                        {
                            // 多频段信号分离
                            var multiRadarList = new List<List<RadarSignal>>();
                            var paramList = satInfo.ParamList;
                            foreach (var pam in paramList)
                            {
                                var tList = targetList.Where(s =>
                                {
                                    bool state = true;

                                    if (!AppContext.UseIPM && s.IPCTName == "脉内无调制")
                                    {
                                        state = false;
                                    }

                                    if (state && !string.IsNullOrWhiteSpace(pam.RFMax))
                                    {
                                        state = s.RFMax <= Convert.ToDouble(pam.RFMax);
                                    }
                                    if (state && !string.IsNullOrWhiteSpace(pam.RFMin))
                                    {
                                        state = s.RFMin >= Convert.ToDouble(pam.RFMin);
                                    }
                                    if (state && !string.IsNullOrWhiteSpace(pam.PRIMax))
                                    {
                                        state = s.PRIMax <= Convert.ToDouble(pam.PRIMax);
                                    }
                                    if (state && !string.IsNullOrWhiteSpace(pam.PRIMin))
                                    {
                                        state = s.PRIMin >= Convert.ToDouble(pam.PRIMin);
                                    }
                                    if (state && !string.IsNullOrWhiteSpace(pam.PWMax))
                                    {
                                        state = s.PWMax <= Convert.ToDouble(pam.PWMax);
                                    }
                                    if (state && !string.IsNullOrWhiteSpace(pam.PWMin))
                                    {
                                        state = s.PWMin >= Convert.ToDouble(pam.PWMin);
                                    }
                                    //其他参数

                                    return state;
                                });
                                if (tList.Count() > 0)
                                {
                                    multiRadarList.Add(tList.ToList());
                                }
                            }
                            
                            // 根据卫星信息重命名卫星
                            sc.SatelliteName = satInfo.SatelliteName;
                            sc.BelongingArea = satInfo.BelongingArea;

                            foreach (var list in multiRadarList)
                            {
                                // 校正统一时间的方位俯仰抖动情况
                                var rsList = SignalSelectionImpl.SignalJitterCorrection(list);
                                var satCastCopy = new SatelliteCast();
                                DataConvert.UpdateValue(sc, ref satCastCopy);
                                groupSignalList.Add(satCastCopy, rsList);
                            }
                        }
                        else
                        {
                            // 无卫星信息
                            var rsList = SignalSelectionImpl.SignalJitterCorrection(targetList.ToList());
                            groupSignalList.Add(sc, rsList);
                        }

                        this.SetLogText($"【ID:{sc.SatelliteNo} Name:{sc.SatelliteName} InTime:{sc.InTimePrint} OutTime:{sc.OutTimePrint} Osp:{sc.ObservePosture}】");
                    }
                    // 反馈进度
                    int outup;
                    if (SignalSelectionImpl.ForPercentageValue(out outup, satCast.Count, index, 20, 50, 90))
                    {
                        this.SetProgressVal(outup);
                    }
                    index++;
                }

                SetFullRaderData();

                this.SetProgressVal(100);
                result.Status = true;

            }
            catch (Exception ex)
            {
                result.Status = false;
                result.Error = ex;
            }
            return result;
        }

        /// <summary>
        /// 任务完成回调
        /// </summary>
        /// <param name="obj"></param>
        private void WorkCompletecallBack(Result result)
        {
            this.btnStartWork.Enabled = true;
            if (result.Status)
            {
                this.btnDetailed.Text = "3.合批数据";

                this.SetLogText($"分选完成，共匹配{groupSignalList.Count}轨");
                MessageBox.Show("分选完成");
            }
            else
            {
                if (result.Error != null)
                {
                    this.SetLogText(result.Error.Message);
                    MessageBox.Show(result.Error.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        /// <summary>
        /// 数据索引
        /// </summary>
        private List<SortedSet<int>> tableIndexs;
        /// <summary>
        /// 读取合批数据
        /// </summary>
        /// <param name="reAny"></param>
        private void SetAnyRaderData(bool reAny = true, List<RaderSignalCapture> rscList = null)
        {
            if (reAny)
            {
                tableIndexs = new List<SortedSet<int>>();
                rscList = new List<RaderSignalCapture>();

                AsyncTask.PostToUI(() =>
                {
                    foreach (var item in groupSignalList)
                    {
                        // 合批数据还需要考虑多种重频类型及脉内特征类型
                        //List<RadarSignal> rsClone = DataConvert.Clone<RadarSignal>(item.Value).Where(s => s.IPCTName != "脉内无调制").ToList();
                        List<RadarSignal> rsClone = DataConvert.Clone<RadarSignal>(item.Value).Where(s => s.PRITName == "固定" && s.IPCTName == "线性调频").ToList();

                        Orbit orbit = orbitList.Find(o => item.Key.SatelliteNo.Equals(o.SatNoradId));
                        // 降倍处理
                        SignalSelectionImpl.Dedoubling(ref rsClone, orbit);

                        // 条件合批
                        var batchSignal = new List<List<RadarSignal>>();
                        var signalTempList = new List<RadarSignal>();  // 一批数据
                        bool isMerge = true; // 合并标记
                        RadarSignal ssTemp = null; // 临时的，用于和下一个进行比较
                        for (var i = 0; i < rsClone.Count; i++)
                        {
                            RadarSignal ss = rsClone[i];
                            if (ssTemp != null)
                            {
                                //isMerge = false;
                                var mills = LocalTimeChange.DateTimeToLong(ss.SynDate) - LocalTimeChange.DateTimeToLong(ssTemp.SynDate);
                                if (mills > SignalSelectionImpl.TimeSpec(AppContext.TimeSpec))
                                {// 超过5秒不被定义为1批次
                                    isMerge = false;
                                }
                                else if (mills > 1000)
                                {
                                    // 1秒内的重周差不大于(10?)
                                    isMerge = !(Math.Abs(ss.PRIMax - ssTemp.PRIMax) / (mills / 1000) > SignalSelectionImpl.PRISpec(AppContext.PRISpec));
                                }
                                else
                                {
                                    isMerge = !(Math.Abs(ss.PRIMax - ssTemp.PRIMax) > SignalSelectionImpl.PRISpec(AppContext.PRISpec));
                                }
                            }
                            if (!isMerge)
                            {
                                isMerge = true;
                                // 分批
                                batchSignal.Add(signalTempList.ToList());
                                signalTempList?.Clear();
                            }
                            ssTemp = ss;
                            signalTempList.Add(ssTemp);
                        }
                        //最后一批记录
                        batchSignal.Add(signalTempList);

                        //批次计算最后输出的结果
                        //var n = 0;
                        foreach (List<RadarSignal> rss in batchSignal)
                        {
                            if (rss.Count == 0) continue;
                            if (rss.Count > 2)
                            {
                                List<List<RadarSignal>> scaList = new List<List<RadarSignal>>();
                                SignalSelectionImpl.DeepPRI(ref scaList, rss);
                                for (var p = 0; p < scaList.Count; p++)
                                {
                                    var idx = new SortedSet<int>();
                                    foreach (var rs in scaList[p])
                                    {
                                        RaderSignalCapture ssc = DataConvert.Mapper<RaderSignalCapture, RadarSignal>(rs);
                                        ssc.SatNo = item.Key.SatelliteNo;
                                        ssc.SatName = item.Key.SatelliteName;
                                        ssc.OrbitTrend = item.Key.OrbitTrend;//OrbitAnalysis.GetOrbitTrendType(siteEquator, item.Key); 
                                        rscList.Add(ssc);
                                        idx.Add(rscList.Count - 1);
                                    }
                                    tableIndexs.Add(idx);

                                    //数据合批后。。。。
                                }
                            }
                            else
                            {

                                RaderSignalCapture ssc = DataConvert.Mapper<RaderSignalCapture, RadarSignal>(rss[0]);
                                ssc.SatNo = item.Key.SatelliteNo;
                                ssc.SatName = item.Key.SatelliteName;
                                ssc.OrbitTrend = item.Key.OrbitTrend;//OrbitAnalysis.GetOrbitTrendType(siteEquator, item.Key); 
                                rscList.Add(ssc);

                                //数据合批后
                                //SignalExcel sExcel = SignalSelectionImpl.TotalSignalList(rss);
                                //sExcel.RadarNo = n + 1;
                                //sExcel.SatStartDate = item.Key.InTime.ToString("yyyy/MM/dd HH:mm:ss").Replace("-", "/");
                                //sExcel.SatEndDate = item.Key.OutTime.ToString("yyyy/MM/dd HH:mm:ss").Replace("-", "/");
                                //sExcel.SatNo = item.Key.SatelliteNo;
                                //sExcel.SatName = item.Key.SatelliteName;
                                //sExcel.ObservePosture = item.Key.ObservePosture;
                                //sExcel.ElvThenTen = item.Key.ElvThenTen ? "是" : "否";
                                //sExcel.BelongingArea = item.Key.BelongingArea;
                                //EciTime eciTime1 = orbit.GetPosition(item.Key.InTime.ToUniversalTime());
                                //GeoTime geoTime1 = new GeoTime(eciTime1);
                                //EciTime eciTime2 = orbit.GetPosition(item.Key.OutTime.ToUniversalTime());
                                //GeoTime geoTime2 = new GeoTime(eciTime2);
                                //var lss = Deduce.DeductionAngle.CalElevation(geoTime1.Altitude, sExcel.PRIValue, sExcel.PWValue);
                                //if (lss.Count > 0)
                                //{
                                //    List<STKSensor> ls = lss.Where(s => s.SideAngle > 20).Take(3).ToList();
                                //    MapPoint lon_lat = SignalSelectionImpl.ImagingLonLat(geoTime1, geoTime2, ls[1].SideAngle, ls[1].ObliqueDistance);
                                //    sExcel.ImagingAreaCoord = $"{ lon_lat.X:f6},{ lon_lat.Y:f6}";
                                //    signalExport.Add(sExcel);
                                //    n++;
                                //}
                            }
                        }
                    }
                });
                
            }
            //this.BeginInvoke(new SetSignalExcelSource(this.SignalExcelSource), new object[] { signalExport });
            
            this.BeginInvoke(new SetSignalCaptureSourceMaker(this.SignalCaptureSourceMaker), new object[] { rscList, tableIndexs });
            dataState = 4;
        }

        /// <summary>
        /// 读取完整数据
        /// </summary>
        private void SetFullRaderData()
        {
            List<RaderSignalCapture> sscList = new List<RaderSignalCapture>();
            AsyncTask.PostToUI(() =>
            {
                foreach (var item in groupSignalList)
                {
                    foreach (RadarSignal rs in item.Value)
                    {
                        RaderSignalCapture ssc = DataConvert.Mapper<RaderSignalCapture, RadarSignal>(rs);
                        ssc.SatNo = item.Key.SatelliteNo;
                        ssc.SatName = item.Key.SatelliteName;
                        ssc.OrbitTrend = item.Key.OrbitTrend;//OrbitAnalysis.GetOrbitTrendType(siteEquator, item.Key); 
                        sscList.Add(ssc);
                    }
                }
            });
            this.BeginInvoke(new SetSignalCaptureSource(this.SignalCaptureSource), new object[] { sscList });
            dataState = 3;
        }
        

        /// <summary>
        /// 切换数据状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDetailed_Click(object sender, EventArgs e)
        {
            ClearDataSource();
            if (dataState == 4 || dataState == 5)
            {
                SetFullRaderData();
                this.btnDetailed.Text = "3.合批数据";
            }
            else if (dataState == 3)
            {
                SetAnyRaderData();
                this.btnDetailed.Text = "3.完整数据";
            }
            else if (dataState == 6)
            {
                SetAnyRaderData(false);
                this.btnDetailed.Text = "3.完整数据";
            }
        }


        #endregion



        #region 输出Excel
        
        /// <summary>
        /// 导出数据到excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcel_Click(object sender, EventArgs e)
        {
            if (this.dataGridView1.ColumnCount == 0)
            {
                MessageBox.Show("无数据，不执行导出");
                return;
            }

            try
            {
                var createStatus = ExcelExport.Create();
                if (!createStatus)
                {
                    this.SetLogText("无法创建Excel对象，Excel正在占用中");
                    MessageBox.Show("无法创建Excel对象，Excel正在占用中", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return;
                }
            }
            catch (Exception)
            {
                this.SetLogText("无法创建Excel对象，您的电脑可能未安装Excel");
                MessageBox.Show("无法创建Excel对象，您的电脑可能未安装Excel", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }

            string fileName = "雷达数据" + DateTime.Now.ToString("yyyyMMddHHmmss");//可以在这里设置默认文件名
            SaveFileDialog saveDialog = new SaveFileDialog();//实例化文件对象
            saveDialog.DefaultExt = "xlsx";//文件默认扩展名
            saveDialog.Filter = "Excel文件|*.xlsx";//获取或设置当前文件名筛选器字符串，该字符串决定对话框的“另存为文件类型”或“文件类型”框中出现的选择内容。
            saveDialog.FileName = fileName;
            saveDialog.ShowDialog();//打开保存窗口给你选择路径和设置文件名
            saveDialog.AddExtension = true;

            var saveFileName = @saveDialog.FileName;
            if (saveFileName.IndexOf(":") < 0) return; //被点了取消

            this.SetLogText("正在导出数据");

            AsyncTask.AsyncPost(() => ExportExcelWork(saveFileName), (r) => ExportExcelWorkCompleted(r));
            
        }

        /// <summary>
        /// 写入数据到excel的工作
        /// </summary>
        private Result ExportExcelWork(string saveFileName)
        {
            var result = new Result();
            result.Data = saveFileName;

            // 导出数据到新表
            ExcelExport.Open();

            if (dataState == 4 || dataState == 5)
            {
                var header = new string[] { "序号", "国家或地区", "目标名称", "国际编号", "过顶开始时间", "过顶结束时间", "成像开始时间", "成像结束时间", "成像时间", "频率", "重复周期", "脉内特征", "脉内带宽", "幅度", "脉宽", "方位", "俯仰", "轨道走向" };
                ExcelExport.WriteHeaders(header, 1);
                this.SetProgressVal(10);

                var rscList = (List<RaderSignalCapture>)this.dataGridView1.DataSource;

                var outRow = 0;
                foreach (var idx in tableIndexs)
                {
                    var min = idx.Min();
                    var rsc = rscList[min];
                    string observePosture;
                    if (rsc.OrbitTrend == OrbitTrendType.eLeftUp)
                    {
                        observePosture = "上行左视";
                    }
                    else if (rsc.OrbitTrend == OrbitTrendType.eRightDown)
                    {
                        observePosture = "下行右视";
                    }
                    else if (rsc.OrbitTrend == OrbitTrendType.eRightUp)
                    {
                        observePosture = "上行右视";
                    }
                    else if (rsc.OrbitTrend == OrbitTrendType.eLeftDown)
                    {
                        observePosture = "下行左视";
                    }
                    else
                    {
                        observePosture = "未知";
                    }

                    var satCast = groupSignalList.Keys.AsParallel().First(cast => cast.SatelliteNo == rsc.SatNo && cast.InTime.CompareTo(rsc.SynDate) < 0 && cast.OutTime.CompareTo(rsc.SynDate) > 0);

                    //写入数值
                    List<DateTime> times = new List<DateTime>();
                    List<double> freq = new List<double>();
                    List<double> pri = new List<double>();
                    List<double> pw = new List<double>();
                    List<double> pm = new List<double>();
                    List<float> pa = new List<float>();
                    foreach (var i in idx)
                    {
                        var rsc2 = rscList[i];
                        times.Add(rsc2.SynDate);
                        freq.Add(rsc2.RFMin);
                        freq.Add(rsc2.RFMax);
                        pm.Add(rsc2.MFInit - rsc2.MFFinal);
                        pri.Add(rsc2.PRIMin);
                        pri.Add(rsc2.PRIMax);
                        pw.Add(rsc2.PWMin);
                        pw.Add(rsc2.PWMax);
                        pa.Add(rsc2.PA);
                    }
                    var satInfo = satDiction.Find(s => s.SatelliteNo == rsc.SatNo);

                    ExcelExport.WriteRow(1, outRow + 2, 1, (outRow + 1));
                    ExcelExport.WriteRow(1, outRow + 2, 2, satInfo?.BelongingArea);
                    ExcelExport.WriteRow(1, outRow + 2, 3, satInfo?.SatelliteName);
                    ExcelExport.WriteRow(1, outRow + 2, 4, rsc.SatNo);

                    ExcelExport.WriteRow(1, outRow + 2, 5, satCast.InTime, "yyyy/MM/dd HH:mm:ss");
                    ExcelExport.WriteRow(1, outRow + 2, 6, satCast.OutTime, "yyyy/MM/dd HH:mm:ss");

                    ExcelExport.WriteRow(1, outRow + 2, 7, times.First(), "yyyy/MM/dd HH:mm:ss");
                    ExcelExport.WriteRow(1, outRow + 2, 8, times.Last(), "yyyy/MM/dd HH:mm:ss");
                    ExcelExport.WriteRow(1, outRow + 2, 9, (times.Last() - times.First()).Seconds + 1);
                    ExcelExport.WriteRow(1, outRow + 2, 10, (int)((freq.Min() + freq.Max()) / 2));
                    ExcelExport.WriteRow(1, outRow + 2, 11, pri.Average(), "f2");
                    ExcelExport.WriteRow(1, outRow + 2, 12, rsc.IPCTName);
                    ExcelExport.WriteRow(1, outRow + 2, 13, pm.Max(), "f0_");
                    var pav = pa.OrderBy(n => n).Where(n => n > 10 || n < 1000);
                    ExcelExport.WriteRow(1, outRow + 2, 14, pav.Count() > 0 ? pav.Average() : pa.Sum(p => p * 35) / 35, "f0");
                    ExcelExport.WriteRow(1, outRow + 2, 15, pw.Max(), "f1");
                    ExcelExport.WriteRow(1, outRow + 2, 16, rsc.InterAZ, "f1");
                    ExcelExport.WriteRow(1, outRow + 2, 17, rsc.InterPIT, "f1");
                    ExcelExport.WriteRow(1, outRow + 2, 18, observePosture);

                    outRow++;
                }
            }
            else
            {
                // 写入标题  
                ExcelExport.WriteHeaders(this.dataGridView1, 1);

                this.SetProgressVal(10);
                //写入数值
                ExcelExport.WriteData(this.dataGridView1, 1, 2);

            }
            ExcelExport.WorksheetStyle1(1);
            ExcelExport.ColumnsAutoFit(1);

            Application.DoEvents();

            this.SetProgressVal(100);

            result.Status = true;
            return result;
        }

        /// <summary>
        /// 写入数据到Excel完成后保存
        /// </summary>
        /// <param name="result"></param>
        private void ExportExcelWorkCompleted(Result result)
        {
            var saveFileName = result.Data.ToString();
            try
            {
                ExcelExport.Save(saveFileName);
            }
            catch (Exception ex)
            {
                this.SetLogText("导出文件时出错，文件可能正被打开");
                this.SetLogText(ex.Message);
                MessageBox.Show("导出文件时出错,文件可能正被打开！\n" + ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            ExcelExport.Close();

            this.SetLogText($"Excel保存成功，路径{saveFileName}");
            MessageBox.Show(Path.GetFileName(saveFileName) + "保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);//提示保存成功
            
        }

        /// <summary>
        /// 导出异常数据到excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void exportErrExcel_Click(object sender, EventArgs e)
        {
            if (errorRsData.Count > 0)
            {
                try
                {
                    var createStatus = ExcelExport.Create();
                    if (!createStatus)
                    {
                        this.SetLogText("无法创建Excel对象，Excel正在占用中");
                        MessageBox.Show("无法创建Excel对象，Excel正在占用中", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        return;
                    }
                }
                catch (Exception)
                {
                    this.SetLogText("无法创建Excel对象，您的电脑可能未安装Excel");
                    MessageBox.Show("无法创建Excel对象，您的电脑可能未安装Excel", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return;
                }

                AsyncTask.AsyncPost(() => ExportErrExcelWork(), (r) => ExportErrExcelWorkCompleted(r));

            }
        }

        /// <summary>
        /// 写入异常数据到excel的工作
        /// </summary>
        private Result ExportErrExcelWork()
        {
            var result = new Result();

            var data = this.dataGridView1.DataSource;
            this.BeginInvoke(new MethodInvoker(delegate
            {
                this.dataGridView1.DataSource = errorRsData;
                this.dataGridView1.Columns["SynDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            }));
            this.SetLogText("正在导出异常数据");

            ExcelExport.Open();
            ExcelExport.WriteHeaders(this.dataGridView1, 1);

            this.SetProgressVal(10);

            //写入数值
            ExcelExport.WriteData(this.dataGridView1, 1, 2);
            ExcelExport.WorksheetStyle1(1);
            ExcelExport.ColumnsAutoFit(1);

            Application.DoEvents();

            this.SetProgressVal(100);

            result.Status = true;
            result.Data = data;

            return result;
        }

        /// <summary>
        /// 写入异常数据到Excel完成后保存
        /// </summary>
        /// <param name="result"></param>
        private void ExportErrExcelWorkCompleted(Result result)
        {
            var rscList = result.Data as List<RaderSignalCapture>;

            string fileName = DateTime.Now.ToString("yyyyMMddHHmmss");//可以在这里设置默认文件名
            fileName = $"{AppContext.AppPath}{@"ErrData/"}Error_{fileName}.xlsx";
            try
            {
                ExcelExport.Save(fileName);              
            }
            catch (Exception ex)
            {
                this.SetLogText("导出文件时出错，文件可能正被打开");
                this.SetLogText(ex.Message);
                MessageBox.Show("导出文件时出错,文件可能正被打开！\n" + ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            ExcelExport.Close();

            this.SetLogText($"Excel保存成功，路径{fileName}");
            MessageBox.Show(Path.GetFileName(fileName) + "保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);//提示保存成功
            
            // 恢复显示完整数据
            SetAnyRaderData(false, rscList);
            this.btnDetailed.Text = "3.完整数据";
        }


        private bool IsExcelOut = false;
        private int OutRow = 0;
        /// <summary>
        /// 开启或关闭EXCEL同步编辑数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnINTEdit_Click(object sender, EventArgs e)
        {
            if (IsExcelOut)
            {
                if (ExcelExport.StartStatus)
                {
                    string saveFileName;
                    // 正在编辑，结束编辑并保存
                    if (!ExcelExport.IsSaveAs(out saveFileName))
                    {
                        string fileName = "雷达数据" + DateTime.Now.ToString("yyyyMMddHHmmss");
                        SaveFileDialog saveDialog = new SaveFileDialog();//实例化文件对象
                        saveDialog.DefaultExt = "xlsx";//文件默认扩展名
                        saveDialog.Filter = "Excel文件|*.xlsx";//获取或设置当前文件名筛选器字符串，该字符串决定对话框的“另存为文件类型”或“文件类型”框中出现的选择内容。
                        saveDialog.FileName = fileName;
                        saveDialog.ShowDialog();//打开保存窗口给你选择路径和设置文件名
                        saveDialog.AddExtension = true;

                        saveFileName = @saveDialog.FileName;
                        if (saveFileName.IndexOf(":") < 0)
                        {
                            return; //被点了取消
                        }
                    }
                    try
                    {
                        ExcelExport.Save(saveFileName);
                    }
                    catch (Exception ex)
                    {
                        this.SetLogText("导出文件时出错，文件可能正被打开");
                        this.SetLogText(ex.Message);
                        MessageBox.Show("导出文件时出错,文件可能正被打开！\n" + ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    }
                    ExcelExport.Close();

                    this.SetLogText($"Excel保存成功，路径{saveFileName}");
                    MessageBox.Show(Path.GetFileName(saveFileName) + " 保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);//提示保存成功
                }
                SetINTEditButtonStatus(true);
            }
            else
            {
                // 开始编辑
                try
                {
                    var createStatus = ExcelExport.Create();
                    if (!createStatus)
                    {
                        this.SetLogText("无法创建Excel对象，Excel正在占用中");
                        MessageBox.Show("无法创建Excel对象，Excel正在占用中", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        return;
                    }
                }
                catch (Exception)
                {
                    this.SetLogText("无法创建Excel对象，您的电脑可能未安装Excel");
                    MessageBox.Show("无法创建Excel对象，您的电脑可能未安装Excel", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return;
                }
                SetINTEditButtonStatus(false);

                // 导出到模板
                ExcelExport.Open(AppContext.AppPath + @"data/雷达底数表模板.xlsx", true);
                ExcelExport.WorksheetStyle1(1);
                ExcelExport.HeaderStyle1(1);

                MonitorExcelState();
            }
        }

        /// <summary>
        /// 监控Excel打开状态
        /// </summary>
        private void MonitorExcelState()
        {
            AsyncTask.AsyncPost(() =>
            {
                while (ExcelExport.StartStatus)
                {
                    System.Threading.Thread.Sleep(1500);
                }
                SetINTEditButtonStatus(true);
                ExcelExport.Close();
            });
        }

        private delegate void SetINTEditText(string text);
        private void INTEditText(string text)
        {
            this.btnINTEdit.Text = text;
        }
        /// <summary>
        /// 设置INTEdit按钮状态，false启用
        /// </summary>
        /// <param name="status"></param>
        private void SetINTEditButtonStatus(bool hasStatus)
        {
            if (!hasStatus)
            {
                IsExcelOut = true;
                this.btnINTEdit.BackColor = Color.OrangeRed;
                this.Invoke(new SetINTEditText(this.INTEditText), new object[] { "正在输出数据" });
            }
            else
            {
                OutRow = 0;
                IsExcelOut = false;
                this.btnINTEdit.BackColor = Color.LimeGreen;
                this.Invoke(new SetINTEditText(this.INTEditText), new object[] { "开启数据编辑" });
            }
        }

        #endregion



        #region 星下点与成像地域

        /// <summary>
        /// 点击了标图
        /// </summary>
        private bool satShow = false;
        /// <summary>
        /// 在地图上标出位置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void mapLocation_Click(object sender, EventArgs e)
        {
            satShow = true;
            DrawSite();
        }

        /// <summary>
        /// 在地图上标出星下点位置（实施）
        /// </summary>
        private void DrawSite()
        {
            if (satShow && dataState > 0)
            {
                if (dataState == 3)
                {
                    this.tabControl1.SelectedTab = tabControl1.TabPages[0];

                    var pb = this.mapPanel;
                    pb.Controls.Clear();
                    this.toolTip1.RemoveAll();
                    foreach (DataGridViewRow row in this.dataGridView1.SelectedRows)
                    {

                        RaderSignalCapture rsc = ((List<RaderSignalCapture>)this.dataGridView1.DataSource)[row.Index];
                        Orbit orbit = orbitList.Find(o => o.SatNoradId == rsc.SatNo);
                        EciTime eciTime = orbit.GetPosition(rsc.SynDate.ToUniversalTime());
                        GeoTime geoTime = new GeoTime(eciTime);

                        Panel l = new Panel();
                        l.BackColor = Color.Transparent;
                        l.Size = new Size(8, 8);
                        //l.Size = new Size(20, 30);
                        //l.BackgroundImage = ResourcesManager.ResourcesRegulator.GetBitmap("Site.png");
                        //l.BackgroundImageLayout = ImageLayout.Zoom;
                        l.Paint += (o, ex) =>
                        {
                            Brush b = new SolidBrush(Color.Yellow);
                            ex.Graphics.FillEllipse(b, ex.ClipRectangle);
                        };

                        MapPoint mp = new MapPoint();
                        mp.X = geoTime.LongitudeDeg;
                        mp.Y = geoTime.LatitudeDeg;
                        mp.H = geoTime.Altitude;
                        MapPoint mp2 = Translator.LonLatToMercator(mp);
                        MapPoint x_y = Translator.MercatorToScreen(mp2, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
                        l.Location = new Point((int)x_y.X - 4, (int)x_y.Y - 4);

                        this.toolTip1.SetToolTip(l, $"目标：({ rsc.SatNo }){ rsc.SatName }\r\n信号时间：{ rsc.SynDate.ToString() }\r\n经纬度：({ mp.X:f6},{ mp.Y:f6})");
                        l.Cursor = Cursors.Hand;
                        l.MouseEnter += (o, ex) =>
                        {
                            l.BringToFront();
                        };
                        pb.Controls.Add(l);

                    }

                }
                else if (dataState == 5)
                {
                    this.tabControl1.SelectedTab = tabControl1.TabPages[0];

                    var pb = this.mapPanel;
                    pb.Controls.Clear();
                    this.toolTip1.RemoveAll();

                    PictureBox pbox = new PictureBox();
                    pbox.Dock = DockStyle.Fill;
                    pbox.BackColor = Color.Transparent;
                    pbox.Paint += (obj, ex) =>
                    {
                        Graphics g = ex.Graphics;

                        foreach (DataGridViewRow row in this.dataGridView1.SelectedRows)
                        {

                            SignalExcel sExcel = ((List<SignalExcel>)this.dataGridView1.DataSource)[row.Index];
                            Orbit orbit = orbitList.Find(o => o.SatNoradId == sExcel.SatNo);
                            var date2 = sExcel.SynStartDate.Split('-');
                            var tStart = orbit.TPlusEpoch(Convert.ToDateTime(sExcel.SynStartDate).ToUniversalTime()).TotalMinutes;
                            var tEnd = orbit.TPlusEpoch(Convert.ToDateTime(sExcel.SynEndDate).ToUniversalTime()).TotalMinutes;
                            List<PointF> path = new List<PointF>();

                            EciTime eciTime1 = orbit.GetPosition(tStart);
                            GeoTime geoTime1 = new GeoTime(eciTime1);

                            if (tStart < tEnd)
                            {
                                do
                                {

                                    EciTime eciTime = orbit.GetPosition(tStart);
                                    GeoTime geoTime = new GeoTime(eciTime);

                                    MapPoint mp_to = OrbitRelatedImpl.LonLatToMercator(geoTime);
                                    MapPoint x_y = Translator.MercatorToScreen(mp_to, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
                                    PointF point = new PointF((int)x_y.X, (int)x_y.Y);
                                    path.Add(point);

                                    tStart = OrbitAnalysis.ConvertTotalMinutes(OrbitAnalysis.ConvertTotalSeconds(tStart) + 1);
                                }
                                while (tEnd > tStart);

                            } 
                            else
                            {
                                tEnd = OrbitAnalysis.ConvertTotalMinutes(OrbitAnalysis.ConvertTotalSeconds(tStart) + 10);
                            }

                            EciTime eciTime2 = orbit.GetPosition(tEnd);
                            GeoTime geoTime2 = new GeoTime(eciTime2);

                            if (path.Count > 1)
                            {
                                Pen p = new Pen(Color.Yellow, 1);
                                g.DrawLines(p, path.ToArray());
                            }
                            else
                            {
                                MapPoint mp_to = OrbitRelatedImpl.LonLatToMercator(geoTime2);
                                MapPoint x_y = Translator.MercatorToScreen(mp_to, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
                                PointF point = new PointF((int)x_y.X, (int)x_y.Y);

                                Brush b = new SolidBrush(Color.Yellow);
                                g.FillEllipse(b, point.X - 2, point.Y - 2, 4, 4);
                            }

                            List<DeductionObject> ls = SignalSelectionImpl
                                                                    .CalElevation(geoTime1.Altitude, sExcel.PRIValue, sExcel.PWValue)
                                                                    .Where(s => s.SideAngle > 20).ToList();
                            if (ls.Count > 0)
                            {
                                int[] x_y = SignalSelectionImpl.ImagingXY(geoTime1, 
                                                                        geoTime2, 
                                                                        pb.ClientRectangle.Width, 
                                                                        pb.ClientRectangle.Height, 
                                                                        ls[1].SideAngle, 
                                                                        ls[1].ObliqueDistance);
                                Pen p0 = new Pen(Color.Crimson, 1);
                                g.DrawEllipse(p0, x_y[0], x_y[1], 5, 8);
                            }
                        }

                    };
                    pbox.MouseMove += (o, ev) =>
                    {
                        mapPanel_MouseMove(pb, ev);
                    };
                    pb.Controls.Add(pbox);

                }
                else
                {
                    MessageBox.Show("无对应数据");
                }
            }
        }


        #endregion



        #region  委托加载DataGridView数据

        //private BindingSource dataBinding = new BindingSource();

        /// <summary>
        /// 合批数据显示，dataState == 5
        /// </summary>
        /// <param name="dt"></param>
        private delegate void SetSignalExcelSource(List<SignalExcel> dt);
        private void SignalExcelSource(List<SignalExcel> dt)
        {
            this.dataGridView1.SuspendLayout();
            this.dataGridView1.DataSource = dt;
            this.dataGridView1.Columns["RadarNo"].HeaderText = "批号";
            this.dataGridView1.Columns["BelongingArea"].HeaderText = "所属国家";
            this.dataGridView1.Columns["SatName"].HeaderText = "目标名称";
            //this.dataGridView1.Columns["SatNo"].Visible = false;
            this.dataGridView1.Columns["SatNo"].HeaderText = "目标编号";
            this.dataGridView1.Columns["SatStartDate"].HeaderText = "过顶开始时间";
            this.dataGridView1.Columns["SatStartDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dataGridView1.Columns["SatEndDate"].HeaderText = "过顶结束时间";
            this.dataGridView1.Columns["SatEndDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dataGridView1.Columns["ElvThenTen"].HeaderText = "侦察俯仰角是否大于10°";
            this.dataGridView1.Columns["SynStartDate"].HeaderText = "成像开始时间";
            this.dataGridView1.Columns["SynStartDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dataGridView1.Columns["SynEndDate"].HeaderText = "成像结束时间";
            this.dataGridView1.Columns["SynEndDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dataGridView1.Columns["SynTimeDura"].HeaderText = "持续时间";
            this.dataGridView1.Columns["RFTName"].HeaderText = "射频类型";
            this.dataGridView1.Columns["RFValue"].HeaderText = "中心频率(MHz)";
            this.dataGridView1.Columns["RFValue"].DefaultCellStyle.Format = "f0";
            this.dataGridView1.Columns["PRITName"].HeaderText = "重频类型";
            this.dataGridView1.Columns["PRIValue"].HeaderText = "重复周期(μs)";
            this.dataGridView1.Columns["PRIValue"].DefaultCellStyle.Format = "f2";
            this.dataGridView1.Columns["PWTName"].HeaderText = "脉宽类型";
            this.dataGridView1.Columns["PWValue"].HeaderText = "脉宽(μs)";
            this.dataGridView1.Columns["PWValue"].DefaultCellStyle.Format = "f1";
            this.dataGridView1.Columns["IPCTName"].HeaderText = "脉内特征";
            this.dataGridView1.Columns["IPCTValue"].HeaderText = "脉内带宽(MHz)"; 
            this.dataGridView1.Columns["InterAZ"].HeaderText = "方位";
            this.dataGridView1.Columns["InterAZ"].DefaultCellStyle.Format = "f1";
            this.dataGridView1.Columns["InterPIT"].HeaderText = "俯仰";
            this.dataGridView1.Columns["InterPIT"].DefaultCellStyle.Format = "f1";
            this.dataGridView1.Columns["PA"].HeaderText = "幅度";
            this.dataGridView1.Columns["ObservePosture"].HeaderText = "轨道走向";
            this.dataGridView1.Columns["ImagingAreaCoord"].HeaderText = "概略经纬度";

            this.dataGridView1.ResumeLayout();
            this.dataGridView1.Refresh();
        }

        /// <summary>
        /// 完整数据显示
        /// </summary>
        /// <param name="dt"></param>
        private delegate void SetSignalCaptureSource(List<RaderSignalCapture> dt);
        private void SignalCaptureSource(List<RaderSignalCapture> dt)
        {
            this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

            this.dataGridView1.SuspendLayout();
            this.dataGridView1.DataSource = dt;
            this.dataGridView1.Columns["SatNo"].HeaderText = "目标编号";
            this.dataGridView1.Columns["SatName"].HeaderText = "目标名称";
            this.dataGridView1.Columns["RadarNo"].HeaderText = "接收批号";
            this.dataGridView1.Columns["SynDate"].HeaderText = "处理时间";
            this.dataGridView1.Columns["SynDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dataGridView1.Columns["InterTime"].HeaderText = "中断时间";
            this.dataGridView1.Columns["InterTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dataGridView1.Columns["SynDateDuration"].HeaderText = "持续时间";
            this.dataGridView1.Columns["RFTName"].HeaderText = "射频类型";
            this.dataGridView1.Columns["RFMin"].HeaderText = "射频最小值";
            this.dataGridView1.Columns["RFMax"].HeaderText = "射频最大值";
            this.dataGridView1.Columns["MFInit"].HeaderText = "频率初始值";
            this.dataGridView1.Columns["MFFinal"].HeaderText = "频率最终值";
            this.dataGridView1.Columns["PRITName"].HeaderText = "重频类型";
            this.dataGridView1.Columns["PRIMin"].HeaderText = "重复周期最小值";
            this.dataGridView1.Columns["PRIMax"].HeaderText = "重复周期最大值";
            this.dataGridView1.Columns["PWTName"].HeaderText = "脉宽类型";
            this.dataGridView1.Columns["PWMin"].HeaderText = "脉宽最小值";
            this.dataGridView1.Columns["PWMax"].HeaderText = "脉宽最大值";
            this.dataGridView1.Columns["IPCTName"].HeaderText = "脉内特征";
            this.dataGridView1.Columns["InterAZ"].HeaderText = "方位";
            this.dataGridView1.Columns["InterPIT"].HeaderText = "俯仰";
            this.dataGridView1.Columns["PA"].HeaderText = "幅度";
            this.dataGridView1.Columns["OrbitTrend"].Visible = false;
            this.dataGridView1.Columns["StatnID"].Visible = false;

            this.dataGridView1.ResumeLayout();
            this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells;
            
        }


        /// <summary>
        /// 查询数据显示
        /// </summary>
        /// <param name="dt"></param>
        private delegate void SetSignalResultSource(List<RadarSatellite> dt);
        private void SignalResultSource(List<RadarSatellite> dt)
        {
            this.dgvResult.SuspendLayout();
            this.dgvResult.DataSource = dt;

            this.dgvResult.Columns["SatNo"].HeaderText = "目标编号";
            this.dgvResult.Columns["SatName"].HeaderText = "目标名称";
            this.dgvResult.Columns["SatArea"].HeaderText = "目标所属国家（地区）";
            this.dgvResult.Columns["InTime"].HeaderText = "进站时间";
            this.dgvResult.Columns["InTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dgvResult.Columns["OutTime"].HeaderText = "出站时间";
            this.dgvResult.Columns["OutTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.dgvResult.Columns["Longitude"].HeaderText = "观测站经度";
            this.dgvResult.Columns["Latitude"].HeaderText = "观测站纬度";
            this.dgvResult.Columns["Altitude"].HeaderText = "观测站高度";
            this.dgvResult.Columns["UseTLE"].Visible = false;
            //this.dgvResult.Columns["SignalList"].Visible = false;

            this.dgvResult.ResumeLayout();
            this.dgvResult.Refresh();
        }

        /// <summary>
        /// 查询数据显示
        /// </summary>
        /// <param name="dt"></param>
        private delegate void SetSignalResultSource2(List<RadarSignalSubmit> dt);
        private void SignalResultSource2(List<RadarSignalSubmit> dt)
        {
            this.dgvResult.DataSource = dt;

            this.dgvResult.Columns["SatNo"].HeaderText = "目标编号";
            this.dgvResult.Columns["RadarNo"].HeaderText = "目标名称";
            this.dgvResult.Columns["SynDate"].HeaderText = "处理时间";
            this.dgvResult.Columns["SynDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss.fff";
            this.dgvResult.Columns["InterTime"].HeaderText = "中断时间";
            this.dgvResult.Columns["InterTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss.fff";
            this.dgvResult.Columns["RFTName"].HeaderText = "射频类型";
            this.dgvResult.Columns["RFMin"].HeaderText = "射频最小值";
            this.dgvResult.Columns["RFMax"].HeaderText = "射频最大值";
            this.dgvResult.Columns["RFValue"].HeaderText = "射频组值";
            this.dgvResult.Columns["MFInit"].HeaderText = "频率初始值";
            this.dgvResult.Columns["MFFinal"].HeaderText = "频率最终值";
            this.dgvResult.Columns["MFValue"].HeaderText = "信号带宽";
            this.dgvResult.Columns["PRITName"].HeaderText = "重频类型";
            this.dgvResult.Columns["PRIMin"].HeaderText = "重复周期最小值";
            this.dgvResult.Columns["PRIMax"].HeaderText = "重复周期最大值";
            this.dgvResult.Columns["PRIValue"].HeaderText = "重复周期组值";
            this.dgvResult.Columns["PWTName"].HeaderText = "脉宽类型";
            this.dgvResult.Columns["PWMin"].HeaderText = "脉宽最小值";
            this.dgvResult.Columns["PWMax"].HeaderText = "脉宽最大值";
            this.dgvResult.Columns["PWValue"].HeaderText = "脉宽组值";
            this.dgvResult.Columns["IPCTName"].HeaderText = "脉内特征";
            this.dgvResult.Columns["InterAZ"].HeaderText = "方位";
            this.dgvResult.Columns["InterPIT"].HeaderText = "俯仰";
            this.dgvResult.Columns["PA"].HeaderText = "幅度";
            this.dgvResult.Columns["Equipment"].HeaderText = "上报装备";
            this.dgvResult.Columns["ReportNo"].Visible = false;
            this.dgvResult.Columns["GuidCode"].Visible = false;

            this.dgvResult.Refresh();
        }

        /// <summary>
        /// 读取-3C数据显示
        /// </summary>
        /// <param name="dt"></param>
        private delegate void SetSignalCollectSource(List<RadarSignalCollect> dt);
        private void SignalCollectSource(List<RadarSignalCollect> dt)
        {
            this.dataGridView1.VirtualMode = true;
            this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            this.dataGridView1.RowCount = dt.Count;
            
            this.dataGridView1.Columns.Add("RadarNo", "批号"); //["RadarNo"].HeaderText = "批号"; //
            //this.dataGridView1.Columns["RadarNo"].Width = 60;
            this.dataGridView1.Columns.Add("SynDate", "处理时间"); //
            //this.dataGridView1.Columns["SynDate"].Width = 220;
            this.dataGridView1.Columns["SynDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss.fff";
            this.dataGridView1.Columns.Add("InterTime", "中断时间");//
            //this.dataGridView1.Columns["InterTime"].Width = 220;
            this.dataGridView1.Columns["InterTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss.fff";
            this.dataGridView1.Columns.Add("RFTName", "射频类型"); //
            //this.dataGridView1.Columns["RFTName"].Width = 100;
            this.dataGridView1.Columns.Add("RFValue", "射频组值"); //
            this.dataGridView1.Columns.Add("RFMin", "射频最小值"); //
            //this.dataGridView1.Columns["RFMin"].DefaultCellStyle.Format = "f2";
            this.dataGridView1.Columns.Add("RFMax", "射频最大值"); //
            //this.dataGridView1.Columns["RFMax"].DefaultCellStyle.Format = "f2";
            this.dataGridView1.Columns.Add("MFValue", "信号带宽"); //
            this.dataGridView1.Columns.Add("PRITName", "重频类型"); //
            //this.dataGridView1.Columns["PRITName"].Width = 80;
            this.dataGridView1.Columns.Add("PRIValue", "重复周期组值"); //
            this.dataGridView1.Columns.Add("PRIMin", "重复周期最小值"); //
            //this.dataGridView1.Columns["PRIMin"].DefaultCellStyle.Format = "f2";
            this.dataGridView1.Columns.Add("PRIMax", "重复周期最大值"); //
            //this.dataGridView1.Columns["PRIMax"].DefaultCellStyle.Format = "f2";
            this.dataGridView1.Columns.Add("PWTName", "脉宽类型"); //
            //this.dataGridView1.Columns["PWTName"].Width = 80;
            this.dataGridView1.Columns.Add("PWValue", "脉宽组值"); //
            this.dataGridView1.Columns.Add("PWMin", "脉宽最小值"); //
            //this.dataGridView1.Columns["PWMin"].DefaultCellStyle.Format = "f2";
            this.dataGridView1.Columns.Add("PWMax", "脉宽最大值"); //
            //this.dataGridView1.Columns["PWMax"].DefaultCellStyle.Format = "f2";/
            this.dataGridView1.Columns.Add("InterAZ", "方位"); //
            //this.dataGridView1.Columns["InterAZ"].DefaultCellStyle.Format = "f1";
            this.dataGridView1.Columns.Add("InterPIT", "俯仰"); //
            //this.dataGridView1.Columns["InterPIT"].DefaultCellStyle.Format = "f1";
            this.dataGridView1.Columns.Add("PA", "幅度"); //

            this.dataGridView1.CellValueNeeded += (sender, e) =>
            {
                var item = dt[e.RowIndex];
                var v = GetValueByColumn(item, e.ColumnIndex);
                e.Value = v;
            };

            this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells;
            //this.dataGridView1.Refresh();
        }

        private object GetValueByColumn<T>(T item, int columnIndex)
        {
            var columnName = dataGridView1.Columns[columnIndex].Name; //.DataPropertyName;
            var property = typeof(T).GetProperty(columnName);
            return property?.GetValue(item);
        }

        #endregion



        #region 分析

        /// <summary>
        /// 推算卫星STK仰角
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void calSTKElevation_Click(object sender, EventArgs e)
        {
            if (dataState == 5)
            {
                SignalExcel se = signalExport.ElementAt(this.dataGridView1.SelectedRows[0].Index);

                ShowElevation seForm = new ShowElevation();
                seForm.StartPosition = FormStartPosition.CenterParent;
                Orbit orbit = orbitList.Find(o => o.SatNoradId == se.SatNo);
                var orbitTime = Convert.ToDateTime(se.SynStartDate).ToUniversalTime();
                EciTime eciTime = orbit.GetPosition(orbitTime);
                GeoTime geoTime = new GeoTime(eciTime);
                seForm.SetElevation(geoTime.Altitude, se.PRIValue, se.PWValue);
                seForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("只对分析后的数据进行推算卫星STK仰角", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        
        /// <summary>
        /// 进行STK仿真
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void STKAnalysisFromSelect_Click(object sender, EventArgs e)
        {
            if (this.dataGridView1.SelectedRows.Count == 1)
            {
                OpenStkEngine();
                //建立通信
                var Commands = (IRemoteContect.Message)Activator.GetObject(typeof(IRemoteContect.Message), "tcp://localhost:52580/RemoteCommands");
                AsyncTask.PostToUI(() =>
                {
                    var rscList = (List<RaderSignalCapture>)this.dataGridView1.DataSource;
                    Commands.PostCommand("TLEPath", tleFilePath);
                    var index = this.dataGridView1.SelectedRows[0].Index;
                    var rsc = rscList[index]; //RaderSignalCapture
                    //object enName = SQLiteHelper.ExecuteQueryScalar($"SELECT SatEnName FROM DB_K_SATELLITE WHERE SatNo = \"{ rsc.SatNo}\"");
                    string enName = satDiction.Find(s => s.SatelliteNo == rsc.SatNo)?.SatelliteEnName ?? $"__{rsc.SatNo}";

                    IRemoteContect.SwapData swap = new IRemoteContect.SwapData();
                    Commands.ReceiveEvent += swap.TriggerSwapData;
                    swap.ReceiveEvent += (r) =>
                    {
                        var args = r as object[];
                        // 回传信息，输出Excel
                        if (IsExcelOut && (dataState == 4 || dataState == 5) && args[0] != null)
                        {
                            string observePosture;
                            if (rsc.OrbitTrend == OrbitTrendType.eLeftUp)
                            {
                                observePosture = "上行左视";
                            }
                            else if (rsc.OrbitTrend == OrbitTrendType.eRightDown)
                            {
                                observePosture = "下行右视";
                            }
                            else if (rsc.OrbitTrend == OrbitTrendType.eRightUp)
                            {
                                observePosture = "上行右视";
                            }
                            else if (rsc.OrbitTrend == OrbitTrendType.eLeftDown)
                            {
                                observePosture = "下行左视";
                            }
                            else
                            {
                                observePosture = "未知";
                            }

                            var indexs = tableIndexs.Find(idxs => idxs.Contains(index));
                            //写入数值
                            List<DateTime> times = new List<DateTime>();
                            List<double> freq = new List<double>();
                            List<double> pri = new List<double>();
                            List<double> pw = new List<double>();
                            List<double> pm = new List<double>();
                            List<float> pa = new List<float>();
                            foreach (var i in indexs)
                            {
                                var rsc2 = rscList[i];
                                times.Add(rsc2.SynDate);
                                freq.Add(rsc2.RFMin);
                                freq.Add(rsc2.RFMax);
                                pm.Add(rsc2.MFInit - rsc2.MFFinal);
                                pri.Add(rsc2.PRIMin);
                                pri.Add(rsc2.PRIMax);
                                pw.Add(rsc2.PWMin);
                                pw.Add(rsc2.PWMax);
                                pa.Add(rsc2.PA);
                            }

                            /***
                             ** 1序号 2中文名称 3英文名称 4国际编号 5自编号 6成像开始时间 7成像结束时间 8中心频率MHz 9重复周期起始 10重复周期结束 
                             ** 11脉内特征 12脉内带宽 13脉宽 14幅度 15截获地域 16单位 17任务信息 18仿真重复周期 19卫星高度KM 20卫星波束侧摆角
                             ** 21轨道视角 22正\斜侧视角 23地距分辨率 24研判成像模式 25研判经纬度 26研判侦照地域 27可信度 28反馈侦照地域
                            ***/
                            ExcelExport.WriteRow(1, OutRow + 3, 1, (OutRow + 1));

                            var satInfo = satDiction.Find(s => s.SatelliteNo == rscList[indexs.First()].SatNo);
                            ExcelExport.WriteRow(1, OutRow + 3, 2, satInfo.SatelliteName);
                            ExcelExport.WriteRow(1, OutRow + 3, 3, satInfo.SatelliteEnName);
                            ExcelExport.WriteRow(1, OutRow + 3, 4, satInfo.SatelliteNo);
                            ExcelExport.WriteRow(1, OutRow + 3, 5, satInfo.SatelliteNumbering);
                            ExcelExport.WriteRow(1, OutRow + 3, 6, times.First(), "yyyy/MM/dd HH:mm:ss");
                            ExcelExport.WriteRow(1, OutRow + 3, 7, times.Last(), "yyyy/MM/dd HH:mm:ss");
                            ExcelExport.WriteRow(1, OutRow + 3, 8, (int)((freq.Min() + freq.Max()) / 2)); //频率中心值
                            ExcelExport.WriteRow(1, OutRow + 3, 9, pri.First(), "f2");
                            ExcelExport.WriteRow(1, OutRow + 3, 10, pri.Last(), "f2");
                            
                            ExcelExport.WriteRow(1, OutRow + 3, 11, rscList[indexs.First()].IPCTName);
                            ExcelExport.WriteRow(1, OutRow + 3, 12, pm.Max() == 0 ? args[3] : pm.Max(), "f0_"); //脉内带宽最大值
                            ExcelExport.WriteRow(1, OutRow + 3, 13, pw.Max(), "f1"); //脉宽最大值
                            var pav = pa.OrderBy(n => n).Where(n => n > 10 || n < 1000);
                            ExcelExport.WriteRow(1, OutRow + 3, 14, pav.Count() > 0 ? pav.Average() : pa.Sum(p => p * 35) / 35, "f0");
                            ExcelExport.WriteRow(1, OutRow + 3, 18, args[1], "f2");
                            ExcelExport.WriteRow(1, OutRow + 3, 19, args[0], "f2");
                            ExcelExport.WriteRow(1, OutRow + 3, 20, args[6], "f2");
                            ExcelExport.WriteRow(1, OutRow + 3, 21, observePosture);
                            ExcelExport.WriteRow(1, OutRow + 3, 22, args[5], "f0");
                            ExcelExport.WriteRow(1, OutRow + 3, 23, args[4], "f2");
                            //worksheet.Cells[r + 3, ?] = ((LocalTimeChange.DateTimeToLong(times.Last()) - LocalTimeChange.DateTimeToLong(times.First())) / 1000 + 1).ToString();
                            //var mode = Math.Abs((pri.First() - pri.Last()) / ((times.Last().Ticks - times.First().Ticks) / 10000000.0));
                            //string modeStr;
                            //if (mode > 1)
                            //{
                            //    modeStr = "滑动聚束式";
                            //}
                            //else if (mode <= 1 && mode >= 0.3)
                            //{
                            //    modeStr = "聚束式";
                            //}
                            //else if (mode < 0.3)
                            //{
                            //    modeStr = "条带式";
                            //}
                            //else
                            //{
                            //    modeStr = "扫描式";  // 如何定义？
                            //}
                            //ExcelExport.WriteRow(1, OutRow + 3, 24, modeStr);
                            ExcelExport.WriteRow(1, OutRow + 3, 25, args[7]);

                            ExcelExport.ColumnsAutoFit(1);
                            OutRow++;

                            Application.DoEvents();
                        }
                    };


                    Commands.PostCommand("Scenario", rsc.SynDate);
                    Commands.PostCommand("Satellite", rsc.SatNo, enName, rsc.SynDate);
                    Commands.PostCommand("Signal", 
                        Math.Round((rsc.PRIMax + rsc.PRIMin) / 2, 3), 
                        Math.Round((rsc.PWMax + rsc.PWMin) / 2, 3),
                        Math.Round(rsc.MFInit - rsc.MFFinal));
                    Commands.PostCommand("Reversed", 
                        rsc.OrbitTrend == OrbitTrendType.eLeftUp || rsc.OrbitTrend == OrbitTrendType.eLeftDown);
                    Commands.PostCommand("Site", 
                        SimplePinYin.GetPinYinInitial(AppContext.SiteEquator.SiteName),
                        AppContext.SiteEquator.SiteLongitude,
                        AppContext.SiteEquator.SiteLatitude,
                        (AppContext.SiteEquator.SiteAltitude / 1000));
                    if (dataState == 4 || dataState == 5)
                    {
                        var idx = tableIndexs.Find(i => i.Contains(index));
                        if (idx != null)
                        {
                            Commands.PostCommand("SignalInterval", rscList[idx.Min()].SynDate, rscList[idx.Max()].SynDate); 
                        }
                    }
                });
            }
        }
        
        /// <summary>
        /// 图表分析
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lineChart_Click(object sender, EventArgs e)
        {
            var selectRows = this.dataGridView1.SelectedRows;
            if (selectRows.Count > 0)
            {
                var rscList = (List<RaderSignalCapture>)this.dataGridView1.DataSource;

                List<double> xvPW = new List<double>();
                List<double> yvPW = new List<double>();
                List<double> xvPRI = new List<double>();
                List<double> yvPRI = new List<double>();
                List<double> xvRF = new List<double>();
                List<double> yvRF = new List<double>();
                List<double> xvPA = new List<double>();
                List<double> yvPA = new List<double>();
                if (selectRows.Count == 1 && (dataState == 4 || dataState == 5))
                {
                    var indexs = tableIndexs.Find(idxs => idxs.Contains(selectRows[0].Index));
                    var tick = rscList[indexs.First()].SynDate.Ticks;
                    foreach (var i in indexs)
                    {
                        RaderSignalCapture rsc = rscList[i];
                        var time = Math.Round((rsc.SynDate.Ticks - tick) / 10000000.0);
                        yvPW.Add((rsc.PWMax + rsc.PWMin) / 2);
                        xvPW.Add(time);
                        yvPRI.Add((rsc.PRIMax + rsc.PRIMin) / 2);
                        xvPRI.Add(time);
                        yvRF.Add((rsc.RFMax + rsc.RFMin) / 2);
                        xvRF.Add(time);
                        yvPA.Add(rsc.PA);
                        xvPA.Add(time);
                    }
                }
                else
                {
                    var tick = (rscList[selectRows[selectRows.Count - 1].Index]).SynDate.Ticks;
                    foreach (DataGridViewRow row in selectRows)
                    {
                        RaderSignalCapture rsc = rscList[row.Index];
                        var time = Math.Round((rsc.SynDate.Ticks - tick) / 10000000.0);
                        yvPW.Add((rsc.PWMax + rsc.PWMin) / 2);
                        xvPW.Add(time);
                        yvPRI.Add((rsc.PRIMax + rsc.PRIMin) / 2);
                        xvPRI.Add(time);
                        yvRF.Add((rsc.RFMax + rsc.RFMin) / 2);
                        xvRF.Add(time);
                    }
                }
                MyCharts mctPW = new MyCharts();
                mctPW.Init(this.chart1);
                mctPW.SetValue(xvPW.ToArray(), yvPW.ToArray());
                MyCharts mctPRI = new MyCharts();
                mctPRI.Init(this.chart2);
                mctPRI.SetValue(xvPRI.ToArray(), yvPRI.ToArray());
                MyCharts mctRF = new MyCharts();
                mctRF.Init(this.chart3);
                mctRF.SetValue(xvRF.ToArray(), yvRF.ToArray());
                MyCharts mctPA = new MyCharts();
                mctPA.Init(this.chart4);
                mctPA.SetValue(xvPA.ToArray(), yvPA.ToArray());

                //this.tabControl1.SelectedTab = tabControl1.TabPages[1];
                this.tabControl1.SelectTab(1);
            }
        }



        #endregion



        #region 合批处理

        /// <summary>
        /// 生成颜色集
        /// </summary>
        private readonly List<int[]> colors = ColorUtil.GenerateRandomColors(30, 180);
        private int _colorspoint = 0;
        private Color GetNextColor()
        {
            var point = _colorspoint < colors.Count ? _colorspoint++ : _colorspoint = 0;
            int[] colorRGB = colors.ElementAt(point);
            return Color.FromArgb(colorRGB[0], colorRGB[1], colorRGB[2]);
        }

        /// <summary>
        /// 对合批的数据进行标记
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="indexs"></param>
        private delegate void SetSignalCaptureSourceMaker(List<RaderSignalCapture> dt, List<SortedSet<int>> indexs);
        private void SignalCaptureSourceMaker(List<RaderSignalCapture> dt, List<SortedSet<int>> indexs)
        {
            SignalCaptureSource(dt);
            if (indexs?.Count > 0)
            {
                var rows = this.dataGridView1.Rows;
                foreach (var idxs in indexs)
                {
                    var color = GetNextColor();
                    foreach (var idx in idxs)
                    {
                        foreach (var ele in rows[idx].Cells)
                        {
                            (ele as DataGridViewCell).Style.BackColor = color;
                        }
                    }
                }

            }
        }

        /// <summary>
        /// 手动标记合批数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void myMark_Click(object sender, EventArgs e)
        {
            var rows = this.dataGridView1.SelectedRows;
            
            var idx = new SortedSet<int>();
            foreach (DataGridViewRow row in rows)
            {
                idx.Add(row.Index);
            }

            if (tableIndexs == null)
            {
                //没有经过自动合批
                tableIndexs = new List<SortedSet<int>>();
                dataState = 5;
            }

            var defaultColor = this.dataGridView1.DefaultCellStyle.BackColor;
            Color acolor = default(Color), bcolor = default(Color);
            var allRow = this.dataGridView1.Rows;
            for (var i = idx.Max(); i < allRow.Count; i++)
            {
                var col = allRow[i].Cells[0].Style.BackColor;
                if (!col.Equals(defaultColor))
                {
                    acolor = col; // 后一个有颜色的项
                    break;
                }
            }

            for (var i = idx.Min(); i > 0; i--)
            {
                var col = allRow[i].Cells[0].Style.BackColor;
                if (!col.Equals(defaultColor))
                {
                    bcolor = col; // 前一个有颜色的项
                    break;
                }
            }

            var color = GetNextColor();
            while (color.Equals(acolor) || color.Equals(bcolor))
            {
                color = GetNextColor();  // 颜色不与前后一致
            }

            foreach (DataGridViewRow row in rows)
            {
                foreach (var ele in row.Cells)
                {
                    (ele as DataGridViewCell).Style.BackColor = color;
                }
            }

            AsyncTask.PostToUI(() =>
            {
                RefreshTableIndexs(idx);
            });

            dataState = 5;
        }


        /// <summary>
        /// 刷新索引
        /// </summary>
        /// <param name="insert"></param>
        private void RefreshTableIndexs(SortedSet<int> insert)
        {
            var minVal = insert.First();
            var maxVal = insert.Last();
            var rindexs = new List<SortedSet<int>>() { insert };
            tableIndexs.ForEach(idxs =>
            {
                idxs.ExceptWith(insert);                
                if (idxs.All(idx => idx < minVal))
                {
                    //抽取大于插入最大值的部分，如果有则分为两份
                    var maxGroup = idxs.Where(idx => idx > maxVal);
                    if (maxGroup.Any())
                    {
                        rindexs.Add(new SortedSet<int>(maxGroup));
                        idxs.RemoveWhere(idx => idx > maxVal);
                    }
                }
            });
            tableIndexs.AddRange(rindexs);
            tableIndexs.RemoveAll(idxs => !idxs.Any());
            tableIndexs.Sort((a, b) => a.First().CompareTo(b.First()));
            
            //tableIndexs.Clear();
            // 如果序列不是连续的则分离
            //foreach (var idx in rindexs)
            //{
            //    if (idx.Any())
            //    {
            //        int initial = idx.ElementAt(0);
            //        int agg = initial;
            //        for (var i = 1; i < idx.Count; i++)
            //        {
            //            var ele = idx.ElementAt(i);
            //            if (ele - 1 == agg)
            //            {
            //                agg = ele;
            //                continue;
            //            }
            //            tableIndexs.Add(idx.GetViewBetween(initial, idx.ElementAt(i - 1)));
            //            agg = initial = idx.ElementAt(i);
            //        }
            //        if (initial != idx.ElementAt(0))
            //        {
            //            tableIndexs.Add(idx.GetViewBetween(initial, agg));
            //        }
            //        else
            //        {
            //            tableIndexs.Add(idx);
            //        }
            //    }
            //}           
        }

        /// <summary>
        /// 手动移出合批数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmMyDel_Click(object sender, EventArgs e)
        {
            if (tableIndexs == null)
                return;
            var rows = this.dataGridView1.SelectedRows;
            var color = this.dataGridView1.DefaultCellStyle.BackColor;

            AsyncTask.PostToUI(() =>
            {
                foreach (DataGridViewRow row in rows)
                {
                    var remove = false;
                    tableIndexs.ForEach(i =>
                    {
                        if (i.Contains(row.Index))
                            remove = i.Remove(row.Index);
                    });
                    if (remove)
                    {
                        foreach (var ele in row.Cells)
                        {
                            (ele as DataGridViewCell).Style.BackColor = color;
                        }
                    }
                }
                tableIndexs.RemoveAll(idxs => !idxs.Any());
            });
            dataState = 5;
        }

        #endregion



        #region 数据提交

        private static readonly object ExecuteLocker = new object();

        /// <summary>
        /// 提交数据到数据库
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSubmitData_Click(object sender, EventArgs e)
        {
            if (tableIndexs?.Count > 0)
            {
                if (MessageBox.Show("确认已检查无错误之后完成提交！", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.OK)
                {
                    int ret = 0;
                    var rscList = this.dataGridView1.DataSource as List<RaderSignalCapture>;
                    lock (ExecuteLocker)
                    {
                        SQLiteHelper.BeginTempSQLiteConnection("PostDB");

                        var commandText1 = "INSERT INTO [RADAR_SATELLITE](SatNo,SatName,SatArea,InTime,OutTime,UseTLE,Longitude,Latitude,Altitude,GuidCode) VALUES(@SatNo,@SatName,@SatArea,@InTime,@OutTime,@UseTLE,@Longitude,@Latitude,@Altitude,@GuidCode)";
                        var commandText2 = "INSERT INTO [RADAR_SIGNAL](SatNo,RadarNo,SynDate,InterTime,RFTName,RFMin,RFMax,RFValue,MFInit,MFFinal,PRITName,PRIMin,PRIMax,PRIValue,PWTName,PWMin,PWMax,PWValue,IPCTName,InterAZ,InterPIT,PA,Equipment,ReportNo,GuidCode) VALUES(@SatNo,@RadarNo,@SynDate,@InterTime,@RFTName,@RFMin,@RFMax,@RFValue,@MFInit,@MFFinal,@PRITName,@PRIMin,@PRIMax,@PRIValue,@PWTName,@PWMin,@PWMax,@PWValue,@IPCTName,@InterAZ,@InterPIT,@PA,@Equipment,@ReportNo,@GuidCode)";
                        using (SQLiteConnection conn = new SQLiteConnection(SQLiteHelper.SQLiteConnectionString))
                        {
                            conn.Open();
                            using (SQLiteTransaction tx = conn.BeginTransaction())
                            {
                                try
                                {
                                    SatelliteCast tempSatCast = null;
                                    string guid = "";
                                    int reportNo = 0;
                                    foreach (var idx in tableIndexs)
                                    {
                                        var min = idx.Min();
                                        var rscFirst = rscList[min];
                                        var satCast = groupSignalList.Keys.AsParallel().First(cast => cast.SatelliteNo == rscFirst.SatNo && cast.InTime.CompareTo(rscFirst.SynDate) < 0 && cast.OutTime.CompareTo(rscFirst.SynDate) > 0);
                                        if (!satCast.Equals(tempSatCast))
                                        {
                                            guid = Guid.NewGuid().ToString("N").ToUpper();
                                            //guid = UlidGenerator.NewUlid();
                                            tempSatCast = satCast;
                                            var tle = selectTle.Find(t => t.NoradNumber == satCast.SatelliteNo);
                                            SQLiteParameter[] sqlparam1 = new SQLiteParameter[] {
                                                new SQLiteParameter("@SatNo", satCast.SatelliteNo),
                                                new SQLiteParameter("@SatName", satCast.SatelliteName),
                                                new SQLiteParameter("@SatArea", satCast.BelongingArea),
                                                new SQLiteParameter("@InTime", satCast.InTime),
                                                new SQLiteParameter("@OutTime", satCast.OutTime),
                                                new SQLiteParameter("@UseTLE", $"{tle.Line1}\r\n{tle.Line2}"),
                                                new SQLiteParameter("@Longitude", CurrentSite.SiteLongitude),
                                                new SQLiteParameter("@Latitude", CurrentSite.SiteLatitude),
                                                new SQLiteParameter("@Altitude", CurrentSite.SiteAltitude),
                                                new SQLiteParameter("@GuidCode", guid)
                                            };
                                            ret += SQLiteHelper.ExecuteNonQuery(tx, commandText1, sqlparam1);
                                            reportNo = 1;
                                        }
                                        foreach (var i in idx)
                                        {
                                            var rsc = DataConvert.Mapper<RadarSignalSubmit, RaderSignalCapture>(rscList[i]);
                                            rsc.Equipment = dbSource.ToString();
                                            rsc.ReportNo = reportNo;
                                            SQLiteParameter[] sqlparam2 = new SQLiteParameter[] {
                                                new SQLiteParameter("@SatNo", rsc.SatNo),
                                                new SQLiteParameter("@RadarNo", rsc.RadarNo),
                                                new SQLiteParameter("@SynDate", rsc.SynDate),
                                                new SQLiteParameter("@InterTime", rsc.InterTime),
                                                new SQLiteParameter("@RFTName", rsc.RFTName),
                                                new SQLiteParameter("@RFMin", rsc.RFMin),
                                                new SQLiteParameter("@RFMax", rsc.RFMax),
                                                new SQLiteParameter("@RFValue", rsc.RFValue),
                                                new SQLiteParameter("@MFInit", rsc.MFInit),
                                                new SQLiteParameter("@MFFinal", rsc.MFFinal),
                                                new SQLiteParameter("@PRITName", rsc.PRITName),
                                                new SQLiteParameter("@PRIMin", rsc.PRIMin),
                                                new SQLiteParameter("@PRIMax", rsc.PRIMax),
                                                new SQLiteParameter("@PRIValue", rsc.PRIValue),
                                                new SQLiteParameter("@PWTName", rsc.PWTName),
                                                new SQLiteParameter("@PWMin", rsc.PWMin),
                                                new SQLiteParameter("@PWMax", rsc.PWMax),
                                                new SQLiteParameter("@PWValue", rsc.PWValue),
                                                new SQLiteParameter("@IPCTName", rsc.IPCTName),
                                                new SQLiteParameter("@InterAZ", rsc.InterAZ),
                                                new SQLiteParameter("@InterPIT", rsc.InterPIT),
                                                new SQLiteParameter("@PA", rsc.PA),
                                                new SQLiteParameter("@Equipment", rsc.Equipment),
                                                new SQLiteParameter("@ReportNo", rsc.ReportNo),
                                                new SQLiteParameter("@GuidCode", guid)
                                            };
                                            SQLiteHelper.ExecuteNonQuery(tx, commandText2, sqlparam2);
                                        }
                                        reportNo++;
                                    }
                                    tx.Commit();
                                }
                                catch (Exception ex)
                                {
                                    NLogHepler.Error(ex.Message);
                                    tx.Rollback();
                                    ret = 0;
                                }
                            }
                        }
                        SQLiteHelper.EndTempSQLiteConnection();
                    }
                    if (ret > 0)
                    {
                        MessageBox.Show("提交成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            else
            {
                MessageBox.Show("无合批数据，无法完成提交", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
        }

        /// <summary>
        /// 根据时间查询数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            SQLiteHelper.BeginTempSQLiteConnection("PostDB");

            //string sqlSat = "SELECT SatNo,SatName,SatArea,InTime,OutTime,UseTLE,Longitude,Latitude,Altitude,GuidCode FROM [RADAR_SATELLITE] WHERE (InTime BETWEEN @StartTime AND @EndTime) OR (OutTime BETWEEN @StartTime AND @EndTime)";
            string sqlSat2 = "SELECT SatNo,RadarNo,SynDate,InterTime,RFTName,RFMin,RFMax,RFValue,MFInit,MFFinal,PRITName,PRIMin,PRIMax,PRIValue,PWTName,PWMin,PWMax,PWValue,IPCTName,InterAZ,InterPIT,PA,Equipment,ReportNo,GuidCode FROM [RADAR_SIGNAL] WHERE SynDate BETWEEN @StartTime AND @EndTime";
            SQLiteParameter[] sqlparam = new SQLiteParameter[] {
                new SQLiteParameter("@StartTime", this.timeUp.DateTimeValue),
                new SQLiteParameter("@EndTime", this.timeDown.DateTimeValue)
            };
            var setData = SQLiteHelper.ExecuteDataSet(sqlSat2, sqlparam);
            var rTable = setData.Tables[0];
            //var rSatList = DataConvert.GetEntityFromDataTable<RadarSatellite>(rTable);
            var rSatList = DataConvert.GetEntityFromDataTable<RadarSignalSubmit>(rTable);

            SQLiteHelper.EndTempSQLiteConnection();

            this.BeginInvoke(new SetSignalResultSource2(this.SignalResultSource2), new object[] { rSatList });

        }



        #endregion

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            Form1 f1 = new Form1();
            f1.Show();
        }
    }
}
