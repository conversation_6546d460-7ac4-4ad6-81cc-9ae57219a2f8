# 雷达信号数据库结构说明

## 📋 概述

本文档详细说明了雷达信号分析系统支持的数据库格式和结构要求。

## 🗄️ 支持的数据格式

### 1. RCL673-4 格式
- **文件类型**：Microsoft Access 数据库文件（.mdb）
- **数据库引擎**：Jet OLEDB 4.0
- **主要特点**：标准的雷达信号数据格式

### 2. RCL673-4（改）格式
- **文件类型**：Microsoft Access 数据库文件（.mdb）
- **数据库引擎**：Jet OLEDB 4.0
- **主要特点**：改进版格式，增强了数据完整性

### 3. RCL673-3（改）格式
- **文件类型**：二进制 RDW 格式文件
- **主要特点**：高效的二进制存储格式

## 📊 数据库表结构

### 主要数据表

#### DB_TG_RADAR（雷达信号主表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| RadarNo | Long | 雷达批号 |
| SynDate | DateTime | 处理时间 |
| InterT | DateTime | 中断时间 |
| RFT | Integer | 射频类型ID |
| PRIT | Integer | 重频类型ID |
| PWT | Integer | 脉宽类型ID |
| IPCT | Integer | 脉内特征类型ID |
| InterAZ | Double | 方位角（度） |
| InterPIT | Double | 俯仰角（度） |
| PA | Single | 脉冲幅度 |
| StatnID | Integer | 站点ID |

#### DB_TG_RADARRF（射频参数表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| RadarNo | Long | 雷达批号（外键） |
| SynDate | DateTime | 处理时间（外键） |
| RFMin | Double | 射频最小值（MHz） |
| RFMax | Double | 射频最大值（MHz） |

#### DB_TG_RADARPRI（重频参数表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| RadarNo | Long | 雷达批号（外键） |
| SynDate | DateTime | 处理时间（外键） |
| PRIMin | Double | 重复周期最小值（μs） |
| PRIMax | Double | 重复周期最大值（μs） |

#### DB_TG_RADARPW（脉宽参数表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| RadarNo | Long | 雷达批号（外键） |
| SynDate | DateTime | 处理时间（外键） |
| PWMin | Double | 脉宽最小值（μs） |
| PWMax | Double | 脉宽最大值（μs） |

#### DB_TG_RADARIPC（脉内特征表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| RadarNo | Long | 雷达批号（外键） |
| SynDate | DateTime | 处理时间（外键） |
| MFInit | Double | 脉内带宽起始值（MHz） |
| MFFinal | Double | 脉内带宽终止值（MHz） |

### 参数类型表

#### DB_PT_RFT（射频类型表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| ELEID | Integer | 类型ID（主键） |
| ELENAME | Text | 类型名称 |

**常见射频类型：**
- 固定射频
- 频率分集
- 射频脉间捷变
- 射频脉组捷变

#### DB_PT_PRIT（重频类型表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| ELEID | Integer | 类型ID（主键） |
| ELENAME | Text | 类型名称 |

**常见重频类型：**
- 固定
- 参差
- 抖动
- 组变

#### DB_PT_PWT（脉宽类型表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| ELEID | Integer | 类型ID（主键） |
| ELENAME | Text | 类型名称 |

**常见脉宽类型：**
- 固定
- 双脉冲
- 多脉冲

#### DB_PT_IPCT（脉内特征类型表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| ELEID | Integer | 类型ID（主键） |
| ELENAME | Text | 类型名称 |

**常见脉内特征类型：**
- 脉内无调制
- 线性调频
- 非线性调频
- 相位编码
- 频率编码

#### DB_CM_STNSTATUS（站点状态表）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| StatnID | Integer | 站点ID（主键） |
| StaAtt | Integer | 站点状态 |
| StaType | Integer | 站点类型 |
| Longitude | Double | 经度（度） |
| Latitude | Double | 纬度（度） |
| Height | Double | 高度（米） |

## 🔗 表关系

```
DB_TG_RADAR (主表)
├── DB_TG_RADARRF (射频参数)
├── DB_TG_RADARPRI (重频参数)  
├── DB_TG_RADARPW (脉宽参数)
├── DB_TG_RADARIPC (脉内特征)
├── DB_PT_RFT (射频类型)
├── DB_PT_PRIT (重频类型)
├── DB_PT_PWT (脉宽类型)
├── DB_PT_IPCT (脉内特征类型)
└── DB_CM_STNSTATUS (站点信息)
```

## 📝 数据要求

### 时间格式
- 所有时间字段使用 DateTime 类型
- 时间精度要求：毫秒级
- 时间范围：2010年1月1日至当前时间

### 角度格式
- 方位角范围：0-360度
- 俯仰角范围：0-90度
- 精度要求：0.1度

### 频率格式
- 单位：MHz
- 精度要求：0.01MHz
- 范围：1-40000MHz

### 时间间隔格式
- 重复周期单位：微秒（μs）
- 脉宽单位：微秒（μs）
- 精度要求：0.01μs

## ⚠️ 数据质量要求

1. **完整性**：所有必填字段不能为空
2. **一致性**：关联表数据必须保持一致
3. **有效性**：数值范围必须在合理区间内
4. **时序性**：时间数据必须按时间顺序排列

## 🛠️ 创建示例数据库

由于涉及复杂的数据库结构，建议使用以下步骤创建测试数据：

1. 使用 Microsoft Access 创建新数据库
2. 按照上述结构创建表和字段
3. 建立表之间的关系
4. 插入测试数据
5. 保存为 .mdb 格式

## 📋 数据验证

系统在载入数据时会进行以下验证：
- 数据库连接有效性
- 表结构完整性
- 数据类型正确性
- 关联关系有效性
- 数值范围合理性
