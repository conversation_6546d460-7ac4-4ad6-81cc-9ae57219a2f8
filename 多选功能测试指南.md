# 数据分选表格多选功能测试指南

## 🚀 快速测试步骤

### 第一步：启动程序并加载示例数据
1. 运行雷达信号分析系统
2. 切换到 **"数据分选"** 标签页
3. 点击 **"1.载入数据"** 按钮
4. 在弹出的对话框中选择 **"是"** 来加载示例数据
5. 系统将自动生成50条示例雷达信号数据

### 第二步：测试基本多选功能

#### 2.1 Ctrl + 点击多选
- 按住 `Ctrl` 键
- 点击不同的行
- 观察多行被选中（高亮显示）
- 状态栏显示选中行数

#### 2.2 Shift + 点击范围选择
- 点击第一行
- 按住 `Shift` 键
- 点击第10行
- 观察第1-10行全部被选中

#### 2.3 直接点击单选
- 直接点击任意一行
- 观察之前的选择被清除，只选中当前行

### 第三步：测试右键菜单多选

#### 3.1 右键已选中行
- 先选中几行数据（使用Ctrl+点击）
- 右键点击其中一个已选中的行
- 观察：多选状态保持不变，右键菜单出现

#### 3.2 右键未选中行（不按Ctrl）
- 先选中几行数据
- 右键点击一个未选中的行（不按Ctrl键）
- 观察：之前的选择被清除，只选中当前右键点击的行

#### 3.3 右键未选中行（按住Ctrl）
- 先选中几行数据
- 按住 `Ctrl` 键，右键点击一个未选中的行
- 观察：之前的选择保持，新行被添加到选择中

### 第四步：测试拖拽多选（数据查询表格专用）

#### 4.1 切换到数据查询页面
- 点击 **"数据查询"** 标签页
- 点击 **"查询"** 按钮（如果有数据的话）

#### 4.2 行头拖拽选择
- 在表格左侧行头区域按下鼠标左键
- 拖拽到其他行
- 释放鼠标，观察连续多行被选中

### 第五步：测试批量操作功能

#### 5.1 导出Excel
- 选中多行数据
- 右键选择 **"导出数据到Excel"**
- 验证只导出选中的数据

#### 5.2 合批操作
- 选中多行数据
- 右键选择 **"合批"**
- 观察选中的行被标记为同一批次（颜色变化）

#### 5.3 移出操作
- 选中已合批的数据行
- 右键选择 **"移出"**
- 观察数据从合批中移除

## 📊 预期结果验证

### 多选状态指示
- ✅ 选中的行应该高亮显示
- ✅ 状态栏显示 "已选择 X 行数据"
- ✅ 多选状态在操作间保持一致

### 键盘快捷键
- ✅ `Ctrl + 点击`：累加选择
- ✅ `Shift + 点击`：范围选择
- ✅ 直接点击：单选并清除其他

### 右键菜单行为
- ✅ 右键已选中行：保持多选状态
- ✅ 右键未选中行：根据Ctrl键决定行为
- ✅ 菜单项根据选择状态启用/禁用

### 批量操作
- ✅ 导出功能处理多选数据
- ✅ 合批功能支持多行操作
- ✅ 移出功能支持多行操作

## 🐛 常见问题排查

### 问题1：无法多选
**可能原因：**
- MultiSelect 属性未正确设置
- SelectionMode 不是 FullRowSelect

**解决方案：**
- 检查设计器中的属性设置
- 重新编译程序

### 问题2：拖拽选择不工作
**可能原因：**
- 鼠标事件处理有冲突
- VirtualMode 影响选择行为

**解决方案：**
- 确保在行头区域拖拽
- 检查事件处理顺序

### 问题3：右键菜单行为异常
**可能原因：**
- 事件处理逻辑错误
- 修饰键检测问题

**解决方案：**
- 检查 CellMouseDown 事件处理
- 验证 Control.ModifierKeys 检测

## 📝 测试记录模板

```
测试日期：_______
测试人员：_______

基本多选功能：
□ Ctrl+点击多选 - 通过/失败
□ Shift+点击范围选择 - 通过/失败  
□ 直接点击单选 - 通过/失败

右键菜单多选：
□ 右键已选中行 - 通过/失败
□ 右键未选中行(无Ctrl) - 通过/失败
□ 右键未选中行(有Ctrl) - 通过/失败

拖拽多选：
□ 行头拖拽选择 - 通过/失败

批量操作：
□ 导出Excel - 通过/失败
□ 合批操作 - 通过/失败
□ 移出操作 - 通过/失败

其他问题：
_________________________
_________________________
```

## 🎯 成功标准

多选功能测试成功的标准：
1. 所有基本多选方式都能正常工作
2. 右键菜单行为符合预期
3. 批量操作功能正常
4. 用户体验流畅，无明显卡顿
5. 状态指示清晰准确

完成以上测试后，数据分选表格的多选功能应该完全可用！
