# SignalAnalyzer SAR雷达数据分析工具 - 测试指南

## 📋 测试环境要求

### 系统要求
- **操作系统**：Windows 10/11
- **框架**：.NET Framework 4.7.2 或更高版本
- **分辨率**：建议 1920×1080 或更高（最低 1200×800）
- **内存**：建议 4GB 或更高

### 测试文件
- `sample_radar_data.txt` - 模拟雷达轨道数据文件
- `satellite_list.txt` - 卫星编号列表文件

## 🚀 完整测试流程

### 第一步：启动应用程序
1. 编译并运行 SignalAnalyzer 项目
2. 验证主窗口正确显示：
   - ✅ 窗口标题：「SAR雷达数据分析工具」
   - ✅ 窗口尺寸：1200×800px
   - ✅ 现代化UI界面，浅灰色背景
   - ✅ 状态栏显示「就绪」

### 第二步：界面布局验证
验证各个功能区域是否正确显示：

#### 文件选择区域（顶部）
- ✅ 标签：「雷达轨道数据文件：」
- ✅ 文本框：白色背景，单线边框
- ✅ 浏览按钮：蓝色背景，白色文字，高度充足
- ✅ 按钮悬停效果：深蓝色背景

#### 卫星列表区域（中上部）
- ✅ 标签：「卫星编号列表 (每行一个或逗号分隔)：」
- ✅ 多行文本框：白色背景，垂直滚动条
- ✅ 两个按钮：「导入列表」和「清空列表」
- ✅ 按钮样式：白色背景，灰色边框，悬停效果

#### 操作按钮区域（中部）
- ✅ 「处理数据」按钮：蓝色背景，居中显示
- ✅ 「生成侦察数据」按钮：绿色背景，居中显示
- ✅ 按钮悬停效果正常

#### 结果显示区域（主要区域）
- ✅ 标签：「处理结果：」
- ✅ 大型文本框：白色背景，双向滚动条
- ✅ 只读状态，Consolas字体

#### 导出区域（底部）
- ✅ 「导出结果」按钮：蓝色背景，右对齐
- ✅ 状态栏：浅灰色背景，显示操作状态

### 第三步：文件选择功能测试

#### 测试3.1：浏览文件功能
1. 点击「浏览文件」按钮
2. 验证文件对话框打开：
   - ✅ 过滤器：「文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*」
3. 选择 `TestData/sample_radar_data.txt`
4. 验证结果：
   - ✅ 文件路径显示在文本框中
   - ✅ 状态栏显示：「已选择文件: [文件路径]」

#### 测试3.2：手动输入文件路径
1. 直接在文件路径文本框中输入文件路径
2. 验证路径可以正常输入和编辑

### 第四步：卫星列表功能测试

#### 测试4.1：手动输入卫星编号
1. 在卫星列表文本框中输入以下内容：
```
37162U
38109U
39462U
```
2. 验证文本框支持多行输入和滚动

#### 测试4.2：导入卫星列表
1. 点击「导入列表」按钮
2. 选择 `TestData/satellite_list.txt`
3. 验证结果：
   - ✅ 卫星编号自动填入文本框
   - ✅ 状态栏显示导入数量
   - ✅ 编号按字母顺序排列

#### 测试4.3：清空列表功能
1. 点击「清空列表」按钮
2. 验证确认对话框出现
3. 点击「是」确认清空
4. 验证结果：
   - ✅ 文本框内容被清空
   - ✅ 状态栏显示「卫星列表已清空」

#### 测试4.4：混合格式输入
在卫星列表中输入混合格式：
```
37162U, 38109U
39462U
41334U, 43145U, 37954U
```
验证系统能正确解析逗号分隔和换行分隔的格式。

### 第五步：数据处理功能测试

#### 测试5.1：完整数据处理流程
1. 确保已选择 `sample_radar_data.txt` 文件
2. 确保卫星列表包含测试编号（如：37162U, 38109U, 39462U）
3. 点击「处理数据」按钮
4. 验证结果：
   - ✅ 结果显示区域显示匹配的数据
   - ✅ 数据格式：卫星名称 + 轨道数据
   - ✅ 状态栏显示处理结果统计

#### 测试5.2：错误处理测试
**测试5.2.1：未选择文件**
1. 清空文件路径
2. 点击「处理数据」
3. 验证警告对话框：「请先选择数据文件！」

**测试5.2.2：空卫星列表**
1. 选择文件但清空卫星列表
2. 点击「处理数据」
3. 验证警告对话框：「卫星列表为空，请先添加卫星编号！」

**测试5.2.3：文件不存在**
1. 手动输入不存在的文件路径
2. 点击「处理数据」
3. 验证错误对话框显示文件读取错误

### 第六步：侦察数据生成测试

#### 测试6.1：正常生成流程
1. 完成数据处理（第五步）
2. 点击「生成侦察数据」按钮
3. 验证结果：
   - ✅ 卫星名称被映射（如：未来成像体系雷达-1 → FIA-1）
   - ✅ 轨道数据被转换
   - ✅ 状态栏显示「侦察数据生成完成」

#### 测试6.2：无数据处理
1. 清空结果显示区域
2. 点击「生成侦察数据」
3. 验证警告对话框：「没有可处理的数据！请先处理数据。」

### 第七步：数据导出功能测试

#### 测试7.1：正常导出流程
1. 确保结果显示区域有数据
2. 点击「导出结果」按钮
3. 在保存对话框中选择保存位置
4. 验证结果：
   - ✅ 文件成功保存
   - ✅ 状态栏显示保存路径
   - ✅ 成功对话框显示

#### 测试7.2：无数据导出
1. 清空结果显示区域
2. 点击「导出结果」
3. 验证警告对话框：「没有可导出的数据！」

### 第八步：配置保存测试

#### 测试8.1：配置自动保存
1. 输入卫星列表
2. 关闭应用程序
3. 重新启动应用程序
4. 验证卫星列表是否自动恢复

#### 测试8.2：配置文件位置
验证配置文件保存在：
`%AppData%\SARRadarAnalyzer\config.txt`

## 🎯 预期测试结果

### 数据处理结果示例
处理后的数据应显示为：
```
未来成像体系雷达-1
轨道数据行1
轨道数据行2内容
轨道数据行3内容

未来成像体系雷达-2
轨道数据行1
轨道数据行2内容
轨道数据行3内容
```

### 侦察数据生成结果示例
生成后的数据应显示为：
```
FIA-1
37162U 10046A 轨道数据...

FIA-2
38109U 12014A 轨道数据...
```

## 🐛 常见问题排查

### 界面显示问题
- **按钮高度不足**：检查TableLayoutPanel行高度设置
- **控件重叠**：验证窗口最小尺寸设置
- **字体显示异常**：确认系统安装微软雅黑字体

### 功能异常问题
- **文件读取失败**：检查文件路径和权限
- **数据格式错误**：验证测试文件格式正确性
- **配置保存失败**：检查AppData目录权限

### 性能问题
- **大文件处理慢**：正常现象，可观察状态栏进度
- **内存占用高**：处理大量数据时的正常表现

## ✅ 测试完成检查清单

- [ ] 界面布局正确显示
- [ ] 文件选择功能正常
- [ ] 卫星列表管理功能正常
- [ ] 数据处理功能正常
- [ ] 侦察数据生成功能正常
- [ ] 数据导出功能正常
- [ ] 错误处理机制正常
- [ ] 配置保存恢复正常
- [ ] 现代化UI样式正确应用
- [ ] 所有按钮和控件响应正常

## 📞 技术支持

如果在测试过程中遇到问题，请检查：
1. .NET Framework版本是否符合要求
2. 测试文件是否在正确位置
3. 应用程序是否有足够的文件系统权限
4. 系统显示设置是否支持应用程序的最小分辨率要求

测试完成后，应用程序应该能够稳定运行并正确处理SAR雷达数据分析任务。
