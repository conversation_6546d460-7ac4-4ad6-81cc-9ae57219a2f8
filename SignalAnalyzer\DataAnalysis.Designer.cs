namespace SignalAnalyzer
{
    partial class DataAnalysis
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DataAnalysis));
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            this.timeStart = new SignalAnalyzer.Controls.TimeInputBox();
            this.timeEnd = new SignalAnalyzer.Controls.TimeInputBox();
            this.timeUp = new SignalAnalyzer.Controls.TimeInputBox();
            this.timeDown = new SignalAnalyzer.Controls.TimeInputBox();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.progressBarDisplay = new System.Windows.Forms.ToolStripProgressBar();
            this.percentageValue = new System.Windows.Forms.ToolStripStatusLabel();
            this.descriptionText = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolSynTime = new System.Windows.Forms.ToolStripStatusLabel();
            this.coordinateText = new System.Windows.Forms.ToolStripStatusLabel();
            this.menuDataOption = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.exportErrExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.mapLocation = new System.Windows.Forms.ToolStripMenuItem();
            this.myAnyalysis = new System.Windows.Forms.ToolStripMenuItem();
            this.calSTKElevation = new System.Windows.Forms.ToolStripMenuItem();
            this.STKAnalysis = new System.Windows.Forms.ToolStripMenuItem();
            this.lineChart = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOperation = new System.Windows.Forms.ToolStripMenuItem();
            this.myMark = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmMyDel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.mapPanel = new SignalAnalyzer.Controls.PanelEnhanced();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.appLog = new System.Windows.Forms.TextBox();
            this.colorDialog1 = new System.Windows.Forms.ColorDialog();
            this.notifyMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.showMain = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.other = new System.Windows.Forms.ToolStripMenuItem();
            this.openOrbit = new System.Windows.Forms.ToolStripMenuItem();
            this.openSTKTool = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.appExit = new System.Windows.Forms.ToolStripMenuItem();
            this.SANotify = new System.Windows.Forms.NotifyIcon(this.components);
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.dgvResult = new System.Windows.Forms.DataGridView();
            this.panel3 = new System.Windows.Forms.Panel();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnQuery = new System.Windows.Forms.Button();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.button1 = new System.Windows.Forms.Button();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.tbSiteInfo = new System.Windows.Forms.TextBox();
            this.btnSubmitData = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.btnDetailed = new System.Windows.Forms.Button();
            this.btnINTEdit = new System.Windows.Forms.Button();
            this.btnClear = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.btnStartWork = new System.Windows.Forms.Button();
            this.btnOpen = new System.Windows.Forms.Button();
            this.mainSplitContainer = new System.Windows.Forms.SplitContainer();
            this.navigationPanel = new System.Windows.Forms.Panel();
            this.btnNavHome = new System.Windows.Forms.Button();
            this.btnNavDataSelection = new System.Windows.Forms.Button();
            this.btnNavChartAnalysis = new System.Windows.Forms.Button();
            this.btnNavDataQuery = new System.Windows.Forms.Button();
            this.btnNavSystemSettings = new System.Windows.Forms.Button();
            this.btnSubSystemSetting = new System.Windows.Forms.Button();
            this.btnSubRadarEdit = new System.Windows.Forms.Button();
            this.btnNavOtherTools = new System.Windows.Forms.Button();
            this.btnSubOrbitPredictor = new System.Windows.Forms.Button();
            this.btnSubSTKSimulator = new System.Windows.Forms.Button();
            this.btnSubSARAnalysis = new System.Windows.Forms.Button();
            this.contentPanel = new System.Windows.Forms.Panel();
            this.homePanel = new System.Windows.Forms.Panel();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.lblWelcome = new System.Windows.Forms.Label();
            this.dataSelectionPanel = new System.Windows.Forms.Panel();
            this.chartAnalysisPanel = new System.Windows.Forms.Panel();
            this.dataQueryPanel = new System.Windows.Forms.Panel();
            this.mapDisplayPanel = new System.Windows.Forms.Panel();
            this.systemSettingPanel = new System.Windows.Forms.Panel();
            this.radarEditPanel = new System.Windows.Forms.Panel();
            this.orbitPredictorPanel = new System.Windows.Forms.Panel();
            this.stkSimulatorPanel = new System.Windows.Forms.Panel();
            this.sarAnalysisPanel = new System.Windows.Forms.Panel();
            this.statusStrip1.SuspendLayout();
            this.menuDataOption.SuspendLayout();
            this.notifyMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvResult)).BeginInit();
            this.panel3.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.mainSplitContainer)).BeginInit();
            this.mainSplitContainer.Panel1.SuspendLayout();
            this.mainSplitContainer.Panel2.SuspendLayout();
            this.mainSplitContainer.SuspendLayout();
            this.navigationPanel.SuspendLayout();
            this.contentPanel.SuspendLayout();
            this.homePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.dataSelectionPanel.SuspendLayout();
            this.chartAnalysisPanel.SuspendLayout();
            this.dataQueryPanel.SuspendLayout();
            this.mapDisplayPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // timeStart
            // 
            this.timeStart.AutoSize = true;
            this.timeStart.BackColor = System.Drawing.Color.White;
            this.timeStart.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeStart.DateTimeValue = new System.DateTime(2010, 1, 1, 0, 0, 0, 0);
            this.timeStart.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.timeStart.Location = new System.Drawing.Point(110, 18);
            this.timeStart.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeStart.MinimumSize = new System.Drawing.Size(178, 28);
            this.timeStart.Name = "timeStart";
            this.timeStart.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeStart.Size = new System.Drawing.Size(238, 28);
            this.timeStart.TabIndex = 15;
            // 
            // timeEnd
            // 
            this.timeEnd.AutoSize = true;
            this.timeEnd.BackColor = System.Drawing.Color.White;
            this.timeEnd.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeEnd.DateTimeValue = new System.DateTime(2010, 1, 1, 0, 0, 0, 0);
            this.timeEnd.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.timeEnd.Location = new System.Drawing.Point(470, 18);
            this.timeEnd.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeEnd.MinimumSize = new System.Drawing.Size(178, 28);
            this.timeEnd.Name = "timeEnd";
            this.timeEnd.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeEnd.Size = new System.Drawing.Size(238, 28);
            this.timeEnd.TabIndex = 16;
            // 
            // timeUp
            // 
            this.timeUp.AutoSize = true;
            this.timeUp.BackColor = System.Drawing.Color.White;
            this.timeUp.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeUp.DateTimeValue = new System.DateTime(2010, 1, 1, 0, 0, 0, 0);
            this.timeUp.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.timeUp.Location = new System.Drawing.Point(115, 24);
            this.timeUp.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeUp.MinimumSize = new System.Drawing.Size(178, 28);
            this.timeUp.Name = "timeUp";
            this.timeUp.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeUp.Size = new System.Drawing.Size(238, 28);
            this.timeUp.TabIndex = 15;
            // 
            // timeDown
            // 
            this.timeDown.AutoSize = true;
            this.timeDown.BackColor = System.Drawing.Color.White;
            this.timeDown.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeDown.DateTimeValue = new System.DateTime(2010, 1, 1, 0, 0, 0, 0);
            this.timeDown.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.timeDown.Location = new System.Drawing.Point(490, 24);
            this.timeDown.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeDown.MinimumSize = new System.Drawing.Size(178, 28);
            this.timeDown.Name = "timeDown";
            this.timeDown.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeDown.Size = new System.Drawing.Size(238, 28);
            this.timeDown.TabIndex = 16;
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.progressBarDisplay,
            this.percentageValue,
            this.descriptionText,
            this.toolSynTime,
            this.coordinateText});
            this.statusStrip1.Location = new System.Drawing.Point(0, 982);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(1, 0, 13, 0);
            this.statusStrip1.ShowItemToolTips = true;
            this.statusStrip1.Size = new System.Drawing.Size(1468, 39);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // progressBarDisplay
            // 
            this.progressBarDisplay.Name = "progressBarDisplay";
            this.progressBarDisplay.Size = new System.Drawing.Size(100, 33);
            // 
            // percentageValue
            // 
            this.percentageValue.Name = "percentageValue";
            this.percentageValue.Size = new System.Drawing.Size(26, 34);
            this.percentageValue.Text = "0%";
            // 
            // descriptionText
            // 
            this.descriptionText.AutoSize = false;
            this.descriptionText.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.descriptionText.Name = "descriptionText";
            this.descriptionText.Padding = new System.Windows.Forms.Padding(20, 0, 0, 0);
            this.descriptionText.Size = new System.Drawing.Size(500, 34);
            this.descriptionText.Text = "欢迎使用！";
            this.descriptionText.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // toolSynTime
            // 
            this.toolSynTime.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.toolSynTime.Name = "toolSynTime";
            this.toolSynTime.Size = new System.Drawing.Size(676, 34);
            this.toolSynTime.Spring = true;
            // 
            // coordinateText
            // 
            this.coordinateText.AutoSize = false;
            this.coordinateText.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.coordinateText.Name = "coordinateText";
            this.coordinateText.Size = new System.Drawing.Size(150, 34);
            this.coordinateText.Text = "0,0";
            this.coordinateText.ToolTipText = "坐标（经度，纬度）";
            // 
            // menuDataOption
            // 
            this.menuDataOption.Font = new System.Drawing.Font("宋体", 12F);
            this.menuDataOption.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuDataOption.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.exportExcel,
            this.exportErrExcel,
            this.mapLocation,
            this.myAnyalysis,
            this.tsmOperation});
            this.menuDataOption.Name = "menuDataOption";
            this.menuDataOption.Size = new System.Drawing.Size(317, 114);
            this.menuDataOption.Opening += new System.ComponentModel.CancelEventHandler(this.menuDataOption_Opening);
            // 
            // exportExcel
            // 
            this.exportExcel.Font = new System.Drawing.Font("宋体", 12F);
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.E)));
            this.exportExcel.Size = new System.Drawing.Size(316, 22);
            this.exportExcel.Text = "导出数据到Excel";
            this.exportExcel.Click += new System.EventHandler(this.btnExcel_Click);
            // 
            // exportErrExcel
            // 
            this.exportErrExcel.Enabled = false;
            this.exportErrExcel.Name = "exportErrExcel";
            this.exportErrExcel.ShortcutKeys = ((System.Windows.Forms.Keys)(((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.Alt) 
            | System.Windows.Forms.Keys.E)));
            this.exportErrExcel.Size = new System.Drawing.Size(316, 22);
            this.exportErrExcel.Text = "导出异常数据到Excel";
            this.exportErrExcel.Click += new System.EventHandler(this.exportErrExcel_Click);
            // 
            // mapLocation
            // 
            this.mapLocation.Font = new System.Drawing.Font("宋体", 12F);
            this.mapLocation.Name = "mapLocation";
            this.mapLocation.Size = new System.Drawing.Size(316, 22);
            this.mapLocation.Text = "地图上位置";
            this.mapLocation.Visible = false;
            this.mapLocation.Click += new System.EventHandler(this.mapLocation_Click);
            // 
            // myAnyalysis
            // 
            this.myAnyalysis.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.calSTKElevation,
            this.STKAnalysis,
            this.lineChart});
            this.myAnyalysis.Name = "myAnyalysis";
            this.myAnyalysis.Size = new System.Drawing.Size(316, 22);
            this.myAnyalysis.Text = "分析";
            this.myAnyalysis.Visible = false;
            // 
            // calSTKElevation
            // 
            this.calSTKElevation.Font = new System.Drawing.Font("宋体", 12F);
            this.calSTKElevation.Name = "calSTKElevation";
            this.calSTKElevation.Size = new System.Drawing.Size(220, 22);
            this.calSTKElevation.Text = "推测STK仰角";
            this.calSTKElevation.Visible = false;
            this.calSTKElevation.Click += new System.EventHandler(this.calSTKElevation_Click);
            // 
            // STKAnalysis
            // 
            this.STKAnalysis.Name = "STKAnalysis";
            this.STKAnalysis.ShortcutKeys = System.Windows.Forms.Keys.F2;
            this.STKAnalysis.Size = new System.Drawing.Size(220, 22);
            this.STKAnalysis.Text = "当前信号STK仿真";
            this.STKAnalysis.Click += new System.EventHandler(this.STKAnalysisFromSelect_Click);
            // 
            // lineChart
            // 
            this.lineChart.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lineChart.Name = "lineChart";
            this.lineChart.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.Q)));
            this.lineChart.Size = new System.Drawing.Size(220, 22);
            this.lineChart.Text = "图表分析";
            this.lineChart.Click += new System.EventHandler(this.lineChart_Click);
            // 
            // tsmOperation
            // 
            this.tsmOperation.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.myMark,
            this.tsmMyDel});
            this.tsmOperation.Name = "tsmOperation";
            this.tsmOperation.Size = new System.Drawing.Size(316, 22);
            this.tsmOperation.Text = "操作";
            this.tsmOperation.Visible = false;
            // 
            // myMark
            // 
            this.myMark.Image = ((System.Drawing.Image)(resources.GetObject("myMark.Image")));
            this.myMark.Name = "myMark";
            this.myMark.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.M)));
            this.myMark.Size = new System.Drawing.Size(168, 26);
            this.myMark.Text = "合批";
            this.myMark.Click += new System.EventHandler(this.myMark_Click);
            // 
            // tsmMyDel
            // 
            this.tsmMyDel.Name = "tsmMyDel";
            this.tsmMyDel.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D)));
            this.tsmMyDel.Size = new System.Drawing.Size(168, 26);
            this.tsmMyDel.Text = "移出";
            this.tsmMyDel.Click += new System.EventHandler(this.tsmMyDel_Click);
            // 
            // tabPage1
            // 
            this.tabPage1.Location = new System.Drawing.Point(0, 0);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(200, 100);
            this.tabPage1.TabIndex = 0;
            // 
            // mapPanel
            // 
            this.mapPanel.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Default;
            this.mapPanel.Location = new System.Drawing.Point(0, 0);
            this.mapPanel.Name = "mapPanel";
            this.mapPanel.Size = new System.Drawing.Size(200, 100);
            this.mapPanel.TabIndex = 0;
            // 
            // textBox2
            // 
            this.textBox2.Dock = System.Windows.Forms.DockStyle.Right;
            this.textBox2.Location = new System.Drawing.Point(1067, 0);
            this.textBox2.Multiline = true;
            this.textBox2.Name = "textBox2";
            this.textBox2.ReadOnly = true;
            this.textBox2.Size = new System.Drawing.Size(200, 842);
            this.textBox2.TabIndex = 3;
            // 
            // toolTip1
            // 
            this.toolTip1.IsBalloon = true;
            // 
            // appLog
            // 
            this.appLog.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.appLog.Location = new System.Drawing.Point(0, 842);
            this.appLog.Margin = new System.Windows.Forms.Padding(4);
            this.appLog.Multiline = true;
            this.appLog.Name = "appLog";
            this.appLog.ReadOnly = true;
            this.appLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.appLog.Size = new System.Drawing.Size(1468, 140);
            this.appLog.TabIndex = 3;
            // 
            // colorDialog1
            // 
            this.colorDialog1.AnyColor = true;
            this.colorDialog1.FullOpen = true;
            this.colorDialog1.SolidColorOnly = true;
            // 
            // notifyMenu
            // 
            this.notifyMenu.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.notifyMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.showMain,
            this.toolStripSeparator1,
            this.other,
            this.toolStripSeparator2,
            this.appExit});
            this.notifyMenu.Name = "notifyMenu";
            this.notifyMenu.Size = new System.Drawing.Size(137, 82);
            // 
            // showMain
            // 
            this.showMain.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.showMain.Name = "showMain";
            this.showMain.Size = new System.Drawing.Size(136, 22);
            this.showMain.Text = "显示主界面";
            this.showMain.Click += new System.EventHandler(this.showMain_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(133, 6);
            // 
            // other
            // 
            this.other.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.openOrbit,
            this.openSTKTool});
            this.other.Name = "other";
            this.other.Size = new System.Drawing.Size(136, 22);
            this.other.Text = "其他功能";
            // 
            // openOrbit
            // 
            this.openOrbit.Name = "openOrbit";
            this.openOrbit.Size = new System.Drawing.Size(148, 22);
            this.openOrbit.Text = "卫星轨道预报";
            this.openOrbit.Click += new System.EventHandler(this.tsmOpenOrbit_Click);
            // 
            // openSTKTool
            // 
            this.openSTKTool.Name = "openSTKTool";
            this.openSTKTool.Size = new System.Drawing.Size(148, 22);
            this.openSTKTool.Text = "STK模拟仿真";
            this.openSTKTool.Click += new System.EventHandler(this.tsmOpenSTKTool_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(133, 6);
            // 
            // appExit
            // 
            this.appExit.Name = "appExit";
            this.appExit.Size = new System.Drawing.Size(136, 22);
            this.appExit.Text = "退出";
            this.appExit.Click += new System.EventHandler(this.appExit_Click);
            // 
            // SANotify
            // 
            this.SANotify.ContextMenuStrip = this.notifyMenu;
            this.SANotify.Icon = ((System.Drawing.Icon)(resources.GetObject("SANotify.Icon")));
            this.SANotify.Text = "雷达信号分析辅助系统";
            this.SANotify.Visible = true;
            // 
            // tabPage4
            // 
            this.tabPage4.Location = new System.Drawing.Point(0, 0);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(200, 100);
            this.tabPage4.TabIndex = 0;
            // 
            // dgvResult
            // 
            this.dgvResult.AllowUserToAddRows = false;
            this.dgvResult.AllowUserToDeleteRows = false;
            this.dgvResult.AllowUserToResizeColumns = false;
            this.dgvResult.AllowUserToResizeRows = false;
            this.dgvResult.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCells;
            this.dgvResult.BackgroundColor = System.Drawing.Color.White;
            this.dgvResult.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvResult.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgvResult.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 11F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvResult.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvResult.ColumnHeadersHeight = 45;
            this.dgvResult.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvResult.ContextMenuStrip = this.menuDataOption;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("宋体", 11F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvResult.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgvResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvResult.EnableHeadersVisualStyles = false;
            this.dgvResult.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(230)))), ((int)(((byte)(237)))));
            this.dgvResult.Location = new System.Drawing.Point(3, 78);
            this.dgvResult.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgvResult.MultiSelect = true;
            this.dgvResult.Name = "dgvResult";
            this.dgvResult.ReadOnly = true;
            this.dgvResult.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            dataGridViewCellStyle3.Font = new System.Drawing.Font("宋体", 11F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(128)))), ((int)(((byte)(150)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvResult.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgvResult.RowHeadersWidth = 51;
            this.dgvResult.RowTemplate.Height = 32;
            this.dgvResult.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvResult.Size = new System.Drawing.Size(1261, 756);
            this.dgvResult.TabIndex = 4;
            this.dgvResult.VirtualMode = true;
            this.dgvResult.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.dataGridView1_RowPostPaint);
            // 
            // panel3
            // 
            this.panel3.BackColor = System.Drawing.Color.White;
            this.panel3.Controls.Add(this.timeDown);
            this.panel3.Controls.Add(this.timeUp);
            this.panel3.Controls.Add(this.label3);
            this.panel3.Controls.Add(this.label4);
            this.panel3.Controls.Add(this.btnQuery);
            this.panel3.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel3.Location = new System.Drawing.Point(3, 3);
            this.panel3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel3.Name = "panel3";
            this.panel3.Padding = new System.Windows.Forms.Padding(20, 15, 20, 15);
            this.panel3.Size = new System.Drawing.Size(1261, 75);
            this.panel3.TabIndex = 5;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.Color.Transparent;
            this.label3.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.label3.Location = new System.Drawing.Point(25, 27);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(84, 20);
            this.label3.TabIndex = 14;
            this.label3.Text = "开始时间：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.BackColor = System.Drawing.Color.Transparent;
            this.label4.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.label4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.label4.Location = new System.Drawing.Point(400, 27);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(84, 20);
            this.label4.TabIndex = 13;
            this.label4.Text = "结束时间：";
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQuery.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(144)))), ((int)(((byte)(226)))));
            this.btnQuery.FlatAppearance.BorderSize = 0;
            this.btnQuery.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(53)))), ((int)(((byte)(122)))), ((int)(((byte)(189)))));
            this.btnQuery.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnQuery.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnQuery.ForeColor = System.Drawing.Color.White;
            this.btnQuery.Location = new System.Drawing.Point(1122, 20);
            this.btnQuery.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(110, 40);
            this.btnQuery.TabIndex = 0;
            this.btnQuery.Text = "查  询";
            this.btnQuery.UseVisualStyleBackColor = false;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // tabPage3
            // 
            this.tabPage3.Location = new System.Drawing.Point(0, 0);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(200, 100);
            this.tabPage3.TabIndex = 0;
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.AutoScroll = true;
            this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(204, 4);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.Size = new System.Drawing.Size(1059, 834);
            this.flowLayoutPanel1.TabIndex = 1;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.WhiteSmoke;
            this.panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel2.Controls.Add(this.button1);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Left;
            this.panel2.Location = new System.Drawing.Point(4, 4);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(200, 834);
            this.panel2.TabIndex = 1;
            // 
            // button1
            // 
            this.button1.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.button1.Location = new System.Drawing.Point(17, 13);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(160, 36);
            this.button1.TabIndex = 0;
            this.button1.Text = "时序分析";
            this.button1.UseVisualStyleBackColor = true;
            // 
            // tabPage2
            // 
            this.tabPage2.Location = new System.Drawing.Point(0, 0);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Size = new System.Drawing.Size(200, 100);
            this.tabPage2.TabIndex = 0;
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.AllowUserToResizeColumns = false;
            this.dataGridView1.AllowUserToResizeRows = false;
            this.dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCells;
            this.dataGridView1.ColumnHeadersHeight = 38;
            this.dataGridView1.ContextMenuStrip = this.menuDataOption;
            this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView1.Location = new System.Drawing.Point(3, 108);
            this.dataGridView1.MultiSelect = true;
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.RowHeadersWidth = 51;
            this.dataGridView1.RowTemplate.Height = 27;
            this.dataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView1.Size = new System.Drawing.Size(1261, 732);
            this.dataGridView1.TabIndex = 1;
            this.dataGridView1.VirtualMode = true;
            this.dataGridView1.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.dataGridView1_RowPostPaint);
            this.dataGridView1.SelectionChanged += new System.EventHandler(this.dataGridView1_SelectionChanged);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.timeEnd);
            this.panel1.Controls.Add(this.timeStart);
            this.panel1.Controls.Add(this.tbSiteInfo);
            this.panel1.Controls.Add(this.btnSubmitData);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.btnDetailed);
            this.panel1.Controls.Add(this.btnINTEdit);
            this.panel1.Controls.Add(this.btnClear);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.btnStartWork);
            this.panel1.Controls.Add(this.btnOpen);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(3, 2);
            this.panel1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1261, 106);
            this.panel1.TabIndex = 3;
            // 
            // tbSiteInfo
            // 
            this.tbSiteInfo.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.tbSiteInfo.Location = new System.Drawing.Point(110, 62);
            this.tbSiteInfo.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbSiteInfo.MaxLength = 8;
            this.tbSiteInfo.Name = "tbSiteInfo";
            this.tbSiteInfo.ReadOnly = true;
            this.tbSiteInfo.Size = new System.Drawing.Size(320, 27);
            this.tbSiteInfo.TabIndex = 0;
            this.tbSiteInfo.Text = "未设置";
            // 
            // btnSubmitData
            // 
            this.btnSubmitData.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSubmitData.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(72)))), ((int)(((byte)(187)))), ((int)(((byte)(120)))));
            this.btnSubmitData.FlatAppearance.BorderSize = 0;
            this.btnSubmitData.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(56)))), ((int)(((byte)(161)))), ((int)(((byte)(105)))));
            this.btnSubmitData.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSubmitData.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnSubmitData.ForeColor = System.Drawing.Color.White;
            this.btnSubmitData.Location = new System.Drawing.Point(1111, 56);
            this.btnSubmitData.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnSubmitData.Name = "btnSubmitData";
            this.btnSubmitData.Size = new System.Drawing.Size(120, 38);
            this.btnSubmitData.TabIndex = 0;
            this.btnSubmitData.Text = "5.提交数据";
            this.btnSubmitData.UseVisualStyleBackColor = false;
            this.btnSubmitData.Click += new System.EventHandler(this.btnSubmitData_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.BackColor = System.Drawing.Color.Transparent;
            this.label5.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.label5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.label5.Location = new System.Drawing.Point(21, 68);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(84, 20);
            this.label5.TabIndex = 13;
            this.label5.Text = "站点信息：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.label1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.label1.Location = new System.Drawing.Point(21, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(84, 20);
            this.label1.TabIndex = 14;
            this.label1.Text = "开始时间：";
            // 
            // btnDetailed
            // 
            this.btnDetailed.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDetailed.BackColor = System.Drawing.Color.White;
            this.btnDetailed.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(230)))), ((int)(((byte)(237)))));
            this.btnDetailed.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnDetailed.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDetailed.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnDetailed.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnDetailed.Location = new System.Drawing.Point(841, 56);
            this.btnDetailed.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnDetailed.Name = "btnDetailed";
            this.btnDetailed.Size = new System.Drawing.Size(120, 38);
            this.btnDetailed.TabIndex = 7;
            this.btnDetailed.Text = "3.完整数据";
            this.btnDetailed.UseVisualStyleBackColor = false;
            this.btnDetailed.Click += new System.EventHandler(this.btnDetailed_Click);
            // 
            // btnINTEdit
            // 
            this.btnINTEdit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnINTEdit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(72)))), ((int)(((byte)(187)))), ((int)(((byte)(120)))));
            this.btnINTEdit.FlatAppearance.BorderSize = 0;
            this.btnINTEdit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(56)))), ((int)(((byte)(161)))), ((int)(((byte)(105)))));
            this.btnINTEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnINTEdit.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnINTEdit.ForeColor = System.Drawing.Color.White;
            this.btnINTEdit.Location = new System.Drawing.Point(976, 56);
            this.btnINTEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnINTEdit.Name = "btnINTEdit";
            this.btnINTEdit.Size = new System.Drawing.Size(120, 38);
            this.btnINTEdit.TabIndex = 0;
            this.btnINTEdit.Text = "4.数据编辑";
            this.btnINTEdit.UseVisualStyleBackColor = false;
            this.btnINTEdit.Click += new System.EventHandler(this.btnINTEdit_Click);
            // 
            // btnClear
            // 
            this.btnClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnClear.BackColor = System.Drawing.Color.White;
            this.btnClear.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(230)))), ((int)(((byte)(237)))));
            this.btnClear.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnClear.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnClear.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnClear.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnClear.Location = new System.Drawing.Point(841, 11);
            this.btnClear.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(120, 38);
            this.btnClear.TabIndex = 8;
            this.btnClear.Text = "0.清除所有";
            this.btnClear.UseVisualStyleBackColor = false;
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.BackColor = System.Drawing.Color.Transparent;
            this.label2.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.label2.Location = new System.Drawing.Point(380, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(84, 20);
            this.label2.TabIndex = 13;
            this.label2.Text = "结束时间：";
            // 
            // btnStartWork
            // 
            this.btnStartWork.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnStartWork.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(246)))), ((int)(((byte)(173)))), ((int)(((byte)(85)))));
            this.btnStartWork.FlatAppearance.BorderSize = 0;
            this.btnStartWork.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(137)))), ((int)(((byte)(54)))));
            this.btnStartWork.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStartWork.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnStartWork.ForeColor = System.Drawing.Color.White;
            this.btnStartWork.Location = new System.Drawing.Point(1111, 11);
            this.btnStartWork.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnStartWork.Name = "btnStartWork";
            this.btnStartWork.Size = new System.Drawing.Size(120, 38);
            this.btnStartWork.TabIndex = 6;
            this.btnStartWork.Text = "2.开始分选";
            this.btnStartWork.UseVisualStyleBackColor = false;
            this.btnStartWork.Click += new System.EventHandler(this.btnStartWork_Click);
            // 
            // btnOpen
            // 
            this.btnOpen.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOpen.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(144)))), ((int)(((byte)(226)))));
            this.btnOpen.FlatAppearance.BorderSize = 0;
            this.btnOpen.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(53)))), ((int)(((byte)(122)))), ((int)(((byte)(189)))));
            this.btnOpen.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnOpen.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnOpen.ForeColor = System.Drawing.Color.White;
            this.btnOpen.Location = new System.Drawing.Point(976, 11);
            this.btnOpen.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnOpen.Name = "btnOpen";
            this.btnOpen.Size = new System.Drawing.Size(120, 38);
            this.btnOpen.TabIndex = 0;
            this.btnOpen.Text = "1.载入数据";
            this.btnOpen.UseVisualStyleBackColor = false;
            this.btnOpen.Click += new System.EventHandler(this.btnOpen_Click);
            // 
            // mainSplitContainer
            // 
            this.mainSplitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mainSplitContainer.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.mainSplitContainer.IsSplitterFixed = true;
            this.mainSplitContainer.Location = new System.Drawing.Point(0, 0);
            this.mainSplitContainer.Name = "mainSplitContainer";
            // 
            // mainSplitContainer.Panel1
            // 
            this.mainSplitContainer.Panel1.Controls.Add(this.navigationPanel);
            this.mainSplitContainer.Panel1MinSize = 60;
            // 
            // mainSplitContainer.Panel2
            // 
            this.mainSplitContainer.Panel2.Controls.Add(this.contentPanel);
            this.mainSplitContainer.Size = new System.Drawing.Size(1468, 842);
            this.mainSplitContainer.SplitterDistance = 200;
            this.mainSplitContainer.SplitterWidth = 1;
            this.mainSplitContainer.TabIndex = 1;
            // 
            // navigationPanel
            // 
            this.navigationPanel.BackColor = System.Drawing.Color.White;
            this.navigationPanel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.navigationPanel.Controls.Add(this.btnNavHome);
            this.navigationPanel.Controls.Add(this.btnNavDataSelection);
            this.navigationPanel.Controls.Add(this.btnNavChartAnalysis);
            this.navigationPanel.Controls.Add(this.btnNavDataQuery);
            this.navigationPanel.Controls.Add(this.btnNavSystemSettings);
            this.navigationPanel.Controls.Add(this.btnSubSystemSetting);
            this.navigationPanel.Controls.Add(this.btnSubRadarEdit);
            this.navigationPanel.Controls.Add(this.btnNavOtherTools);
            this.navigationPanel.Controls.Add(this.btnSubOrbitPredictor);
            this.navigationPanel.Controls.Add(this.btnSubSTKSimulator);
            this.navigationPanel.Controls.Add(this.btnSubSARAnalysis);
            this.navigationPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.navigationPanel.Location = new System.Drawing.Point(0, 0);
            this.navigationPanel.Name = "navigationPanel";
            this.navigationPanel.Size = new System.Drawing.Size(200, 842);
            this.navigationPanel.TabIndex = 0;
            // 
            // btnNavHome
            // 
            this.btnNavHome.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.btnNavHome.FlatAppearance.BorderSize = 0;
            this.btnNavHome.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnNavHome.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavHome.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnNavHome.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnNavHome.Location = new System.Drawing.Point(0, 0);
            this.btnNavHome.Name = "btnNavHome";
            this.btnNavHome.Size = new System.Drawing.Size(198, 50);
            this.btnNavHome.TabIndex = 0;
            this.btnNavHome.Text = "🏠 首页";
            this.btnNavHome.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNavHome.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNavHome.UseVisualStyleBackColor = false;
            // 
            // btnNavDataSelection
            // 
            this.btnNavDataSelection.BackColor = System.Drawing.Color.White;
            this.btnNavDataSelection.FlatAppearance.BorderSize = 0;
            this.btnNavDataSelection.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnNavDataSelection.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavDataSelection.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnNavDataSelection.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnNavDataSelection.Location = new System.Drawing.Point(0, 50);
            this.btnNavDataSelection.Name = "btnNavDataSelection";
            this.btnNavDataSelection.Size = new System.Drawing.Size(198, 50);
            this.btnNavDataSelection.TabIndex = 1;
            this.btnNavDataSelection.Text = "📊 数据分选";
            this.btnNavDataSelection.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNavDataSelection.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNavDataSelection.UseVisualStyleBackColor = false;
            this.btnNavDataSelection.Click += new System.EventHandler(this.btnNavDataSelection_Click_1);
            // 
            // btnNavChartAnalysis
            // 
            this.btnNavChartAnalysis.BackColor = System.Drawing.Color.White;
            this.btnNavChartAnalysis.FlatAppearance.BorderSize = 0;
            this.btnNavChartAnalysis.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnNavChartAnalysis.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavChartAnalysis.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnNavChartAnalysis.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnNavChartAnalysis.Location = new System.Drawing.Point(0, 100);
            this.btnNavChartAnalysis.Name = "btnNavChartAnalysis";
            this.btnNavChartAnalysis.Size = new System.Drawing.Size(198, 50);
            this.btnNavChartAnalysis.TabIndex = 2;
            this.btnNavChartAnalysis.Text = "📈 图表分析";
            this.btnNavChartAnalysis.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNavChartAnalysis.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNavChartAnalysis.UseVisualStyleBackColor = false;
            // 
            // btnNavDataQuery
            // 
            this.btnNavDataQuery.BackColor = System.Drawing.Color.White;
            this.btnNavDataQuery.FlatAppearance.BorderSize = 0;
            this.btnNavDataQuery.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnNavDataQuery.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavDataQuery.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnNavDataQuery.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnNavDataQuery.Location = new System.Drawing.Point(0, 150);
            this.btnNavDataQuery.Name = "btnNavDataQuery";
            this.btnNavDataQuery.Size = new System.Drawing.Size(198, 50);
            this.btnNavDataQuery.TabIndex = 3;
            this.btnNavDataQuery.Text = "🔍 数据查询";
            this.btnNavDataQuery.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNavDataQuery.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNavDataQuery.UseVisualStyleBackColor = false;
            // 
            // btnNavSystemSettings
            // 
            this.btnNavSystemSettings.BackColor = System.Drawing.Color.White;
            this.btnNavSystemSettings.FlatAppearance.BorderSize = 0;
            this.btnNavSystemSettings.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnNavSystemSettings.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavSystemSettings.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnNavSystemSettings.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnNavSystemSettings.Location = new System.Drawing.Point(0, 200);
            this.btnNavSystemSettings.Name = "btnNavSystemSettings";
            this.btnNavSystemSettings.Size = new System.Drawing.Size(198, 50);
            this.btnNavSystemSettings.TabIndex = 4;
            this.btnNavSystemSettings.Text = "⚙️ 系统设置";
            this.btnNavSystemSettings.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNavSystemSettings.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNavSystemSettings.UseVisualStyleBackColor = false;
            // 
            // btnSubSystemSetting
            // 
            this.btnSubSystemSetting.BackColor = System.Drawing.Color.White;
            this.btnSubSystemSetting.FlatAppearance.BorderSize = 0;
            this.btnSubSystemSetting.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnSubSystemSetting.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSubSystemSetting.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnSubSystemSetting.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(128)))), ((int)(((byte)(150)))));
            this.btnSubSystemSetting.Location = new System.Drawing.Point(20, 250);
            this.btnSubSystemSetting.Name = "btnSubSystemSetting";
            this.btnSubSystemSetting.Size = new System.Drawing.Size(178, 35);
            this.btnSubSystemSetting.TabIndex = 5;
            this.btnSubSystemSetting.Text = "  系统设置";
            this.btnSubSystemSetting.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSubSystemSetting.UseVisualStyleBackColor = false;
            this.btnSubSystemSetting.Visible = false;
            // 
            // btnSubRadarEdit
            // 
            this.btnSubRadarEdit.BackColor = System.Drawing.Color.White;
            this.btnSubRadarEdit.FlatAppearance.BorderSize = 0;
            this.btnSubRadarEdit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnSubRadarEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSubRadarEdit.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnSubRadarEdit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(128)))), ((int)(((byte)(150)))));
            this.btnSubRadarEdit.Location = new System.Drawing.Point(20, 285);
            this.btnSubRadarEdit.Name = "btnSubRadarEdit";
            this.btnSubRadarEdit.Size = new System.Drawing.Size(178, 35);
            this.btnSubRadarEdit.TabIndex = 6;
            this.btnSubRadarEdit.Text = "  雷达参数编辑";
            this.btnSubRadarEdit.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSubRadarEdit.UseVisualStyleBackColor = false;
            this.btnSubRadarEdit.Visible = false;
            // 
            // btnNavOtherTools
            // 
            this.btnNavOtherTools.BackColor = System.Drawing.Color.White;
            this.btnNavOtherTools.FlatAppearance.BorderSize = 0;
            this.btnNavOtherTools.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnNavOtherTools.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavOtherTools.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.btnNavOtherTools.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.btnNavOtherTools.Location = new System.Drawing.Point(0, 320);
            this.btnNavOtherTools.Name = "btnNavOtherTools";
            this.btnNavOtherTools.Size = new System.Drawing.Size(198, 50);
            this.btnNavOtherTools.TabIndex = 7;
            this.btnNavOtherTools.Text = "🔧 其他工具";
            this.btnNavOtherTools.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNavOtherTools.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNavOtherTools.UseVisualStyleBackColor = false;
            // 
            // btnSubOrbitPredictor
            // 
            this.btnSubOrbitPredictor.BackColor = System.Drawing.Color.White;
            this.btnSubOrbitPredictor.FlatAppearance.BorderSize = 0;
            this.btnSubOrbitPredictor.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnSubOrbitPredictor.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSubOrbitPredictor.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnSubOrbitPredictor.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(128)))), ((int)(((byte)(150)))));
            this.btnSubOrbitPredictor.Location = new System.Drawing.Point(20, 370);
            this.btnSubOrbitPredictor.Name = "btnSubOrbitPredictor";
            this.btnSubOrbitPredictor.Size = new System.Drawing.Size(178, 35);
            this.btnSubOrbitPredictor.TabIndex = 8;
            this.btnSubOrbitPredictor.Text = "  卫星轨道预报";
            this.btnSubOrbitPredictor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSubOrbitPredictor.UseVisualStyleBackColor = false;
            this.btnSubOrbitPredictor.Visible = false;
            // 
            // btnSubSTKSimulator
            // 
            this.btnSubSTKSimulator.BackColor = System.Drawing.Color.White;
            this.btnSubSTKSimulator.FlatAppearance.BorderSize = 0;
            this.btnSubSTKSimulator.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnSubSTKSimulator.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSubSTKSimulator.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnSubSTKSimulator.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(128)))), ((int)(((byte)(150)))));
            this.btnSubSTKSimulator.Location = new System.Drawing.Point(20, 405);
            this.btnSubSTKSimulator.Name = "btnSubSTKSimulator";
            this.btnSubSTKSimulator.Size = new System.Drawing.Size(178, 35);
            this.btnSubSTKSimulator.TabIndex = 9;
            this.btnSubSTKSimulator.Text = "  STK模拟器";
            this.btnSubSTKSimulator.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSubSTKSimulator.UseVisualStyleBackColor = false;
            this.btnSubSTKSimulator.Visible = false;
            // 
            // btnSubSARAnalysis
            // 
            this.btnSubSARAnalysis.BackColor = System.Drawing.Color.White;
            this.btnSubSARAnalysis.FlatAppearance.BorderSize = 0;
            this.btnSubSARAnalysis.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.btnSubSARAnalysis.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSubSARAnalysis.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnSubSARAnalysis.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(128)))), ((int)(((byte)(150)))));
            this.btnSubSARAnalysis.Location = new System.Drawing.Point(20, 440);
            this.btnSubSARAnalysis.Name = "btnSubSARAnalysis";
            this.btnSubSARAnalysis.Size = new System.Drawing.Size(178, 35);
            this.btnSubSARAnalysis.TabIndex = 10;
            this.btnSubSARAnalysis.Text = "  SAR雷达分析";
            this.btnSubSARAnalysis.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSubSARAnalysis.UseVisualStyleBackColor = false;
            this.btnSubSARAnalysis.Visible = false;
            // 
            // contentPanel
            // 
            this.contentPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.contentPanel.Controls.Add(this.homePanel);
            this.contentPanel.Controls.Add(this.dataSelectionPanel);
            this.contentPanel.Controls.Add(this.chartAnalysisPanel);
            this.contentPanel.Controls.Add(this.dataQueryPanel);
            this.contentPanel.Controls.Add(this.mapDisplayPanel);
            this.contentPanel.Controls.Add(this.systemSettingPanel);
            this.contentPanel.Controls.Add(this.radarEditPanel);
            this.contentPanel.Controls.Add(this.orbitPredictorPanel);
            this.contentPanel.Controls.Add(this.stkSimulatorPanel);
            this.contentPanel.Controls.Add(this.sarAnalysisPanel);
            this.contentPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.contentPanel.Location = new System.Drawing.Point(0, 0);
            this.contentPanel.Name = "contentPanel";
            this.contentPanel.Size = new System.Drawing.Size(1267, 842);
            this.contentPanel.TabIndex = 0;
            // 
            // homePanel
            // 
            this.homePanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.homePanel.Controls.Add(this.pictureBox1);
            this.homePanel.Controls.Add(this.lblWelcome);
            this.homePanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.homePanel.Location = new System.Drawing.Point(0, 0);
            this.homePanel.Name = "homePanel";
            this.homePanel.Size = new System.Drawing.Size(1267, 842);
            this.homePanel.TabIndex = 0;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Image = ((System.Drawing.Image)(resources.GetObject("pictureBox1.Image")));
            this.pictureBox1.Location = new System.Drawing.Point(3, 113);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(1258, 697);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox1.TabIndex = 1;
            this.pictureBox1.TabStop = false;
            // 
            // lblWelcome
            // 
            this.lblWelcome.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Font = new System.Drawing.Font("微软雅黑", 24F, System.Drawing.FontStyle.Bold);
            this.lblWelcome.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(55)))), ((int)(((byte)(72)))));
            this.lblWelcome.Location = new System.Drawing.Point(473, 36);
            this.lblWelcome.Name = "lblWelcome";
            this.lblWelcome.Size = new System.Drawing.Size(338, 42);
            this.lblWelcome.TabIndex = 0;
            this.lblWelcome.Text = "欢迎使用信号分析系统";
            this.lblWelcome.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dataSelectionPanel
            // 
            this.dataSelectionPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.dataSelectionPanel.Controls.Add(this.dataGridView1);
            this.dataSelectionPanel.Controls.Add(this.panel1);
            this.dataSelectionPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataSelectionPanel.Location = new System.Drawing.Point(0, 0);
            this.dataSelectionPanel.Name = "dataSelectionPanel";
            this.dataSelectionPanel.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dataSelectionPanel.Size = new System.Drawing.Size(1267, 842);
            this.dataSelectionPanel.TabIndex = 1;
            this.dataSelectionPanel.Visible = false;
            // 
            // chartAnalysisPanel
            // 
            this.chartAnalysisPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.chartAnalysisPanel.Controls.Add(this.flowLayoutPanel1);
            this.chartAnalysisPanel.Controls.Add(this.panel2);
            this.chartAnalysisPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartAnalysisPanel.Location = new System.Drawing.Point(0, 0);
            this.chartAnalysisPanel.Name = "chartAnalysisPanel";
            this.chartAnalysisPanel.Padding = new System.Windows.Forms.Padding(4);
            this.chartAnalysisPanel.Size = new System.Drawing.Size(1267, 842);
            this.chartAnalysisPanel.TabIndex = 2;
            this.chartAnalysisPanel.Visible = false;
            // 
            // dataQueryPanel
            // 
            this.dataQueryPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.dataQueryPanel.Controls.Add(this.dgvResult);
            this.dataQueryPanel.Controls.Add(this.panel3);
            this.dataQueryPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataQueryPanel.Location = new System.Drawing.Point(0, 0);
            this.dataQueryPanel.Name = "dataQueryPanel";
            this.dataQueryPanel.Padding = new System.Windows.Forms.Padding(3, 3, 3, 8);
            this.dataQueryPanel.Size = new System.Drawing.Size(1267, 842);
            this.dataQueryPanel.TabIndex = 3;
            this.dataQueryPanel.Visible = false;
            // 
            // mapDisplayPanel
            // 
            this.mapDisplayPanel.BackColor = System.Drawing.Color.Black;
            this.mapDisplayPanel.Controls.Add(this.mapPanel);
            this.mapDisplayPanel.Controls.Add(this.textBox2);
            this.mapDisplayPanel.Cursor = System.Windows.Forms.Cursors.SizeAll;
            this.mapDisplayPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mapDisplayPanel.Location = new System.Drawing.Point(0, 0);
            this.mapDisplayPanel.Name = "mapDisplayPanel";
            this.mapDisplayPanel.Size = new System.Drawing.Size(1267, 842);
            this.mapDisplayPanel.TabIndex = 4;
            this.mapDisplayPanel.Visible = false;
            // 
            // systemSettingPanel
            // 
            this.systemSettingPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.systemSettingPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.systemSettingPanel.Location = new System.Drawing.Point(0, 0);
            this.systemSettingPanel.Name = "systemSettingPanel";
            this.systemSettingPanel.Size = new System.Drawing.Size(1267, 842);
            this.systemSettingPanel.TabIndex = 5;
            this.systemSettingPanel.Visible = false;
            // 
            // radarEditPanel
            // 
            this.radarEditPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.radarEditPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.radarEditPanel.Location = new System.Drawing.Point(0, 0);
            this.radarEditPanel.Name = "radarEditPanel";
            this.radarEditPanel.Size = new System.Drawing.Size(1267, 842);
            this.radarEditPanel.TabIndex = 6;
            this.radarEditPanel.Visible = false;
            // 
            // orbitPredictorPanel
            // 
            this.orbitPredictorPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.orbitPredictorPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.orbitPredictorPanel.Location = new System.Drawing.Point(0, 0);
            this.orbitPredictorPanel.Name = "orbitPredictorPanel";
            this.orbitPredictorPanel.Size = new System.Drawing.Size(1267, 842);
            this.orbitPredictorPanel.TabIndex = 7;
            this.orbitPredictorPanel.Visible = false;
            // 
            // stkSimulatorPanel
            // 
            this.stkSimulatorPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.stkSimulatorPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.stkSimulatorPanel.Location = new System.Drawing.Point(0, 0);
            this.stkSimulatorPanel.Name = "stkSimulatorPanel";
            this.stkSimulatorPanel.Size = new System.Drawing.Size(1267, 842);
            this.stkSimulatorPanel.TabIndex = 8;
            this.stkSimulatorPanel.Visible = false;
            // 
            // sarAnalysisPanel
            // 
            this.sarAnalysisPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(250)))), ((int)(((byte)(252)))));
            this.sarAnalysisPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sarAnalysisPanel.Location = new System.Drawing.Point(0, 0);
            this.sarAnalysisPanel.Name = "sarAnalysisPanel";
            this.sarAnalysisPanel.Size = new System.Drawing.Size(1267, 842);
            this.sarAnalysisPanel.TabIndex = 9;
            this.sarAnalysisPanel.Visible = false;
            // 
            // DataAnalysis
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1468, 1021);
            this.Controls.Add(this.mainSplitContainer);
            this.Controls.Add(this.appLog);
            this.Controls.Add(this.statusStrip1);
            this.Font = new System.Drawing.Font("宋体", 11F);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.MinimumSize = new System.Drawing.Size(1024, 739);
            this.Name = "DataAnalysis";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "雷达信号分析";
            this.Activated += new System.EventHandler(this.DataAnalysis_Activated);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Main_FormClosing);
            this.Load += new System.EventHandler(this.Main_Load);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.menuDataOption.ResumeLayout(false);
            this.notifyMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvResult)).EndInit();
            this.panel3.ResumeLayout(false);
            this.panel3.PerformLayout();
            this.panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.mainSplitContainer.Panel1.ResumeLayout(false);
            this.mainSplitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.mainSplitContainer)).EndInit();
            this.mainSplitContainer.ResumeLayout(false);
            this.navigationPanel.ResumeLayout(false);
            this.contentPanel.ResumeLayout(false);
            this.homePanel.ResumeLayout(false);
            this.homePanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.dataSelectionPanel.ResumeLayout(false);
            this.chartAnalysisPanel.ResumeLayout(false);
            this.dataQueryPanel.ResumeLayout(false);
            this.mapDisplayPanel.ResumeLayout(false);
            this.mapDisplayPanel.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripProgressBar progressBarDisplay;
        private System.Windows.Forms.ToolStripStatusLabel percentageValue;
        private System.Windows.Forms.ToolStripStatusLabel descriptionText;
        private System.Windows.Forms.ContextMenuStrip menuDataOption;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.ToolStripMenuItem mapLocation;
        private System.Windows.Forms.ToolStripStatusLabel toolSynTime;
        private System.Windows.Forms.ToolStripStatusLabel coordinateText;
        private SignalAnalyzer.Controls.PanelEnhanced mapPanel;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.TextBox appLog;
        private System.Windows.Forms.ToolStripMenuItem myAnyalysis;
        private System.Windows.Forms.ToolStripMenuItem calSTKElevation;
        private System.Windows.Forms.ToolStripMenuItem STKAnalysis;
        private System.Windows.Forms.ToolStripMenuItem lineChart;
        private System.Windows.Forms.ToolStripMenuItem myMark;
        private System.Windows.Forms.ColorDialog colorDialog1;
        private System.Windows.Forms.ToolStripMenuItem exportErrExcel;
        private System.Windows.Forms.ContextMenuStrip notifyMenu;
        private System.Windows.Forms.ToolStripMenuItem showMain;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem appExit;
        private System.Windows.Forms.NotifyIcon SANotify;
        private System.Windows.Forms.ToolStripMenuItem tsmOperation;
        private System.Windows.Forms.ToolStripMenuItem tsmMyDel;
        private System.Windows.Forms.ToolStripMenuItem other;
        private System.Windows.Forms.ToolStripMenuItem openOrbit;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem openSTKTool;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.DataGridView dgvResult;
        private System.Windows.Forms.Panel panel3;
        private Controls.TimeInputBox timeUp;
        private Controls.TimeInputBox timeDown;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnQuery;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart1;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart2;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart3;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart4;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.Panel panel1;
        private Controls.TimeInputBox timeStart;
        private Controls.TimeInputBox timeEnd;
        private System.Windows.Forms.TextBox tbSiteInfo;
        private System.Windows.Forms.Button btnSubmitData;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnDetailed;
        private System.Windows.Forms.Button btnINTEdit;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnStartWork;
        private System.Windows.Forms.Button btnOpen;
        private System.Windows.Forms.SplitContainer mainSplitContainer;
        private System.Windows.Forms.Panel navigationPanel;
        private System.Windows.Forms.Button btnNavHome;
        private System.Windows.Forms.Button btnNavDataSelection;
        private System.Windows.Forms.Button btnNavChartAnalysis;
        private System.Windows.Forms.Button btnNavDataQuery;
        private System.Windows.Forms.Button btnNavSystemSettings;
        private System.Windows.Forms.Button btnSubSystemSetting;
        private System.Windows.Forms.Button btnSubRadarEdit;
        private System.Windows.Forms.Button btnNavOtherTools;
        private System.Windows.Forms.Button btnSubOrbitPredictor;
        private System.Windows.Forms.Button btnSubSTKSimulator;
        private System.Windows.Forms.Button btnSubSARAnalysis;
        private System.Windows.Forms.Panel contentPanel;
        private System.Windows.Forms.Panel homePanel;
        private System.Windows.Forms.Panel dataSelectionPanel;
        private System.Windows.Forms.Panel chartAnalysisPanel;
        private System.Windows.Forms.Panel dataQueryPanel;
        private System.Windows.Forms.Panel mapDisplayPanel;
        private System.Windows.Forms.Panel systemSettingPanel;
        private System.Windows.Forms.Panel radarEditPanel;
        private System.Windows.Forms.Panel orbitPredictorPanel;
        private System.Windows.Forms.Panel stkSimulatorPanel;
        private System.Windows.Forms.Panel sarAnalysisPanel;
        private System.Windows.Forms.Label lblWelcome;
        private System.Windows.Forms.PictureBox pictureBox1;
    }
}

