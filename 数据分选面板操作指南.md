# 雷达信号分析系统 - 数据分选面板操作指南

## 📋 概述

数据分选面板是雷达信号分析系统的核心功能模块，用于对雷达信号数据进行智能分选和目标识别。本指南将详细介绍如何使用该面板进行数据分析。

## 🚀 操作步骤

### 第一步：准备数据文件

#### 1.1 雷达信号数据文件
系统支持以下三种数据格式：

- **RCL673-4**：Microsoft Access 数据库文件（.mdb）
- **RCL673-4（改）**：改进版 Microsoft Access 数据库文件（.mdb）
- **RCL673-3（改）**：二进制 RDW 格式文件

#### 1.2 TLE轨道数据文件
- 文件格式：标准 TLE（Two-Line Element）格式
- 文件扩展名：.tle 或 .txt
- 编码：UTF-8 或 ASCII

### 第二步：载入数据（按钮：1.载入数据）

1. 点击 **"1.载入数据"** 按钮
2. 在弹出的数据类型选择对话框中：
   - 选择数据类型（RCL673-4、RCL673-4（改）或 RCL673-3（改））
   - 点击 **"选择数据库"** 按钮，选择雷达信号数据文件
   - 点击 **"选择TLE"** 按钮，选择卫星轨道数据文件
   - 点击 **"完成"** 按钮

3. 系统将自动：
   - 读取雷达信号数据
   - 加载TLE轨道数据
   - 弹出TLE目标选择对话框

4. 在TLE目标选择对话框中：
   - 勾选需要分析的卫星目标
   - 可使用 **"全选"** 复选框快速选择所有目标
   - 点击 **"确定"** 完成选择

### 第三步：设置分析参数

1. **时间范围设置**：
   - 系统会自动设置开始时间和结束时间
   - 可手动调整时间范围（建议不超过7天）

2. **站点信息确认**：
   - 查看站点信息显示是否正确
   - 如有坐标不一致提示，选择是否使用数据文件中的坐标

### 第四步：开始分选（按钮：2.开始分选）

1. 点击 **"2.开始分选"** 按钮
2. 系统将执行以下步骤：
   - 预报卫星轨道
   - 计算过境时间和角度
   - 进行空域匹配
   - 进行频域匹配
   - 生成分选结果

3. 分选过程中可在状态栏查看进度

### 第五步：查看分选结果

#### 5.1 完整数据查看（按钮：3.完整数据）
- 点击 **"3.完整数据"** 按钮查看详细的信号数据
- 表格显示所有匹配的信号记录

#### 5.2 合批数据查看（按钮：3.合批数据）
- 分选完成后，按钮文本变为 **"3.合批数据"**
- 点击查看经过合并处理的数据结果

### 第六步：多选操作（新功能）

#### 6.1 多选方式
- **Ctrl + 点击**：选择多个不连续的行
- **Shift + 点击**：选择连续的行范围
- **直接点击**：选择单行并清除之前的选择

#### 6.2 右键菜单多选
- 右键点击已选中的行：保持当前多选状态
- 右键点击未选中的行：
  - 不按 Ctrl：清除之前选择，只选择当前行
  - 按住 Ctrl：保持之前选择，添加当前行

#### 6.3 多选功能
选中多行后，可以进行以下操作：
- **导出数据到Excel**：导出选中的数据
- **合批**：将选中的信号合并为一个批次
- **移出**：从结果中移除选中的数据
- **地图上位置**：在地图上显示选中信号的位置
- **分析功能**：对选中数据进行各种分析

### 第七步：数据导出和分析

1. **导出Excel**：
   - 右键点击表格，选择 **"导出数据到Excel"**
   - 选择保存位置和文件名

2. **STK仿真分析**：
   - 选择单行数据，右键选择 **"当前信号STK仿真"**
   - 系统将启动STK模拟器进行轨道仿真

3. **图表分析**：
   - 选择数据行，右键选择 **"图表分析"**
   - 查看信号参数的时序变化图表

### 第八步：数据编辑（可选）

1. 点击 **"开启数据编辑"** 按钮
2. 可以手动修改分选结果
3. 完成编辑后点击 **"5.提交数据"** 保存修改

## ⚠️ 注意事项

1. **TLE数据时效性**：
   - TLE数据超过一周可能导致分选不准确
   - 建议定期更新TLE文件

2. **时间范围限制**：
   - 分析时间跨度建议不超过7天
   - 时间跨度过长会显著增加处理时间

3. **数据文件完整性**：
   - 确保雷达信号数据文件完整且格式正确
   - 确保TLE文件包含所需的卫星轨道数据

4. **系统性能**：
   - 大数据量分析时请耐心等待
   - 可通过状态栏监控处理进度

## 📊 状态信息说明

- **进度条**：显示当前操作的完成百分比
- **状态文本**：显示当前操作的详细信息
- **坐标显示**：鼠标悬停时显示地理坐标或统计信息
- **日志窗口**：显示操作历史和错误信息

## 🔧 故障排除

1. **数据载入失败**：
   - 检查文件路径和权限
   - 确认文件格式正确
   - 查看日志窗口的错误信息

2. **分选结果为空**：
   - 检查时间范围设置
   - 确认TLE数据包含相关卫星
   - 验证站点坐标设置

3. **程序响应缓慢**：
   - 减少分析时间范围
   - 减少选择的TLE目标数量
   - 检查系统资源使用情况
