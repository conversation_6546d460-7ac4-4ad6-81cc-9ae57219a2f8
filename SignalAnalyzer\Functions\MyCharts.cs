﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms.DataVisualization.Charting;

namespace SignalAnalyzer.Functions
{
    public class MyCharts
    {
        private Chart chart;
        private Series series;


        public void Init(Chart chart)
        {
            if (chart == null) return;

            this.chart = chart;

            // 检查并确保有Series
            if (chart.Series.Count == 0)
            {
                chart.Series.Add("DefaultSeries");
            }

            // 检查并确保有ChartArea
            if (chart.ChartAreas.Count == 0)
            {
                chart.ChartAreas.Add(new ChartArea("DefaultArea"));
            }

            series = chart.Series[0];
            series.MarkerSize = 8;
            series.MarkerStyle = MarkerStyle.Circle;
            series.MarkerColor = System.Drawing.Color.CadetBlue;
            series.IsValueShownAsLabel = true;
            //chart.GetToolTipText += Chart_GetToolTipText;
            chart.MouseMove += Chart_MouseMove;
            chart.ChartAreas[0].CursorX.LineColor = System.Drawing.Color.Silver;
        }

        private void Chart_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (chart == null || chart.ChartAreas.Count == 0) return;

            try
            {
                var chartArea = chart.ChartAreas[0];
                var xVal = chartArea.AxisX.PixelPositionToValue(e.X);
                var yVal = chartArea.AxisY.PixelPositionToValue(e.Y);
                if (xVal != -1 && xVal < chartArea.AxisX.Maximum && yVal != -1 && yVal < chartArea.AxisY.Maximum)
                {
                    chartArea.CursorX.Position = xVal;
                    //chartArea.CursorY.Position = yVal;
                }
            }
            catch (Exception)
            {
                // 忽略鼠标移动时的异常，避免影响用户体验
            }
        }

        private void Chart_GetToolTipText(object sender, ToolTipEventArgs e)
        {
            if (chart == null) return;

            try
            {
                HitTestResult result = chart.HitTest(e.X, e.Y); //获取命中的测试结果
                if(result.ChartElementType == ChartElementType.DataPoint && result.Series != null)
                {
                    var i = result.PointIndex;
                    if (i >= 0 && i < result.Series.Points.Count)
                    {
                        var dp = result.Series.Points[i];
                        if (dp.YValues != null && dp.YValues.Length > 0)
                        {
                            var yValue = dp.YValues[0];
                            e.Text = $"{ yValue :f2}";
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 忽略工具提示异常，避免影响用户体验
            }
        }

        public void SetValue(double[] xVal, double[] yVal)
        {
            if (series == null || xVal == null || yVal == null) return;

            try
            {
                if (xVal.Length > 10)
                {
                    series.IsValueShownAsLabel = false;
                }

                // 确保数据长度匹配
                if (xVal.Length == yVal.Length && xVal.Length > 0)
                {
                    series.Points.DataBindXY(xVal, yVal);
                }
            }
            catch (Exception)
            {
                // 数据绑定失败时清空数据点
                series.Points.Clear();
            }
        }
    }
}
