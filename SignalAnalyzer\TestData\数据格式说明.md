# SignalAnalyzer 数据格式说明和测试指南

## 📋 数据格式要求

### 雷达轨道数据文件格式
SignalAnalyzer期望的数据文件格式为**管道符分隔（|）**的文本文件，每行至少包含**15个字段**：

```
字段0|卫星ID|字段2|字段3|卫星名称|字段5|字段6|字段7|字段8|字段9|字段10|字段11|字段12|字段13|轨道数据部分|更多轨道数据...
```

### 字段说明
- **字段1（索引1）**：卫星ID - 用于匹配卫星列表中的编号
- **字段4（索引4）**：卫星名称 - 显示在处理结果中
- **字段14及以后（索引14+）**：轨道数据 - TLE格式或其他轨道参数

### 示例数据行
```
FIELD0|37162U|FIELD2|FIELD3|未来成像体系雷达-1|FIELD5|FIELD6|FIELD7|FIELD8|FIELD9|FIELD10|FIELD11|FIELD12|FIELD13|1 37162U 10046A 10046.12345678 .00001234 00000-0 12345-4 0 9999|2 37162U 045.6789 123.4567 0012345 234.5678 125.4321 15.12345678123456
```

## 🧪 测试步骤

### 步骤1：准备测试环境
1. 确保 `TestData` 文件夹存在
2. 确认测试文件：
   - `sample_radar_data.txt` - 雷达数据文件
   - `satellite_list.txt` - 卫星编号列表
   - 本说明文件

### 步骤2：启动应用程序
1. 编译并运行 SignalAnalyzer
2. 验证界面正确显示（1200×800窗口）

### 步骤3：加载测试数据
1. **选择数据文件**：
   - 点击「浏览文件」按钮
   - 选择 `TestData/sample_radar_data.txt`
   - 确认文件路径显示在文本框中

2. **导入卫星列表**：
   - 点击「导入列表」按钮
   - 选择 `TestData/satellite_list.txt`
   - 确认卫星编号列表显示在文本框中

### 步骤4：处理数据
1. 点击「处理数据」按钮
2. **预期结果**：
   - 状态栏显示：「处理完成，找到 15 条匹配数据」
   - 结果区域显示匹配的卫星名称和轨道数据

### 步骤5：验证处理结果
处理结果应该显示类似以下格式：
```
未来成像体系雷达-1
1 37162U 10046A 10046.12345678 .00001234 00000-0 12345-4 0 9999
2 37162U 045.6789 123.4567 0012345 234.5678 125.4321 15.12345678123456

未来成像体系雷达-2
1 38109U 12014A 12014.23456789 .00002345 00000-0 23456-4 0 9999
2 38109U 056.7890 234.5678 0023456 345.6789 236.5432 15.23456789234567

...（更多卫星数据）
```

## 🔍 故障排除

### 问题：找到0条匹配数据
**可能原因**：
1. **数据格式错误**：文件不是管道符分隔格式
2. **字段数量不足**：每行少于15个字段
3. **卫星ID不匹配**：列表中的ID与文件中的ID不一致
4. **编码问题**：文件编码不是UTF-8

**解决方案**：
1. 检查数据文件格式，确保使用 `|` 分隔
2. 确认每行至少有15个字段
3. 验证卫星ID完全匹配（包括大小写）
4. 使用UTF-8编码保存文件

### 问题：部分数据未匹配
**可能原因**：
1. 卫星ID格式不一致
2. 数据行格式不规范

**解决方案**：
1. 检查卫星列表中的ID格式
2. 确认数据文件中每行的格式一致

## 📊 测试数据说明

### 包含的卫星类型
测试文件包含以下类型的卫星数据：
- **未来成像体系雷达**：5颗（37162U, 38109U, 39462U, 41334U, 43145U）
- **地中海盆地卫星**：3颗（37954U, 39061U, 40381U）
- **SARah系列**：1颗（31598U）
- **合成孔径雷达**：1颗（32376U）
- **I2P系列**：1颗（33412U）
- **本影系列**：2颗（60541U, 60547U）
- **信息收集卫星**：2颗（42072U, 43495U）

### 数据特点
- 每个卫星包含标准的TLE格式轨道数据
- 卫星名称使用中文，便于识别
- 轨道参数使用真实的数值范围
- 涵盖不同年代的卫星（2007-2024）

## ✅ 验证清单

测试完成后，请确认以下项目：
- [ ] 文件选择功能正常
- [ ] 卫星列表导入成功
- [ ] 数据处理找到15条匹配数据
- [ ] 结果显示格式正确
- [ ] 卫星名称正确显示
- [ ] 轨道数据完整显示
- [ ] 状态栏信息准确
- [ ] 导出功能正常工作

## 📝 自定义测试数据

如需创建自定义测试数据，请遵循以下格式：
```
任意字段|卫星ID|任意字段|任意字段|卫星名称|字段5|字段6|字段7|字段8|字段9|字段10|字段11|字段12|字段13|轨道数据行1|轨道数据行2|...更多数据
```

**重要提示**：
- 必须使用管道符 `|` 分隔
- 至少15个字段
- 卫星ID在第2个位置（索引1）
- 卫星名称在第5个位置（索引4）
- 轨道数据从第15个位置开始（索引14）

这样就能确保数据处理功能正常工作！
