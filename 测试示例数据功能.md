# 示例数据功能测试指南

## 功能概述
为数据分选模块添加了示例数据加载功能，用于测试多选功能。

## 新增功能
1. **示例数据按钮**: 在数据分选界面添加了"示例数据"按钮
2. **示例数据生成**: 自动生成50条雷达信号示例数据
3. **示例卫星信息**: 创建5个示例卫星的信息和分选结果

## 测试步骤

### 1. 启动应用程序
- 运行 SignalAnalyzer 应用程序
- 进入数据分选界面

### 2. 加载示例数据
- 点击"示例数据"按钮
- 系统会自动生成并加载示例数据
- 查看进度条显示加载进度
- 确认弹出成功提示对话框

### 3. 验证数据加载
- 检查数据表格中是否显示了示例数据
- 验证数据包含以下字段：
  - 雷达编号 (RadarNo)
  - 同步时间 (SynDate)
  - 中断时间 (InterTime)
  - 方位角 (InterAZ)
  - 俯仰角 (InterPIT)
  - 射频范围 (RFMin/RFMax)
  - 重频范围 (PRIMin/PRIMax)
  - 脉宽范围 (PWMin/PWMax)
  - 幅度 (PA)
  - 脉内特征 (IPCTName)

### 4. 测试多选功能
- **单选测试**: 点击单行，验证行被选中
- **多选测试**: 按住 Ctrl 键点击多行，验证多行被选中
- **范围选择**: 点击第一行，按住 Shift 键点击另一行，验证范围内所有行被选中
- **右键菜单**: 在选中状态下右键点击，验证上下文菜单正常显示

### 5. 验证统计信息
- 选择多行数据后，查看底部状态栏
- 验证显示的统计信息包括：
  - 幅度平均值
  - 中心频率
  - 重复周期平均值
  - 脉宽最大值
  - 脉内带宽最大值
  - 持续时间

## 示例数据内容

### 雷达信号数据 (50条)
- 编号: 1-50
- 时间: 基于当前日期生成
- 方位角: 0-360度随机
- 俯仰角: 5-85度随机
- 射频: 2000-3500MHz范围
- 重频: 100-700μs范围
- 脉宽: 1-20μs范围
- 幅度: 30-80dB范围
- 脉内特征: 线性调频、脉内无调制、相位编码

### 卫星信息 (5个)
1. 国际空间站 (25544) - 国际
2. 北斗导航卫星 (43013) - 中国
3. GPS卫星 (37849) - 美国
4. 格洛纳斯卫星 (40128) - 俄罗斯
5. 伽利略卫星 (41549) - 欧盟

## 预期结果
- 示例数据成功加载到数据表格中
- 多选功能正常工作
- 统计信息正确显示
- 右键菜单功能正常
- 无异常或错误发生

## 注意事项
- 示例数据仅用于测试目的
- 数据为随机生成，不代表真实的雷达信号
- 加载示例数据会清除之前的数据
- 建议在测试环境中使用此功能

## 故障排除
如果遇到问题：
1. 检查是否有编译错误
2. 确认所有依赖项正确引用
3. 验证数据库连接正常
4. 查看应用程序日志获取详细错误信息
