﻿namespace SignalAnalyzer
{
    partial class DataAnalysis
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DataAnalysis));
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.progressBarDisplay = new System.Windows.Forms.ToolStripProgressBar();
            this.percentageValue = new System.Windows.Forms.ToolStripStatusLabel();
            this.descriptionText = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolSynTime = new System.Windows.Forms.ToolStripStatusLabel();
            this.coordinateText = new System.Windows.Forms.ToolStripStatusLabel();
            this.menuDataOption = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.exportErrExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.mapLocation = new System.Windows.Forms.ToolStripMenuItem();
            this.myAnyalysis = new System.Windows.Forms.ToolStripMenuItem();
            this.calSTKElevation = new System.Windows.Forms.ToolStripMenuItem();
            this.STKAnalysis = new System.Windows.Forms.ToolStripMenuItem();
            this.lineChart = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOperation = new System.Windows.Forms.ToolStripMenuItem();
            this.myMark = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmMyDel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.appLog = new System.Windows.Forms.TextBox();
            this.colorDialog1 = new System.Windows.Forms.ColorDialog();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripDropDownButton1 = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmSystemSetting = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmParamEdit = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripDropDownButton2 = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmOpenOrbit = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOpenSTKTool = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.notifyMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.showMain = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.other = new System.Windows.Forms.ToolStripMenuItem();
            this.openOrbit = new System.Windows.Forms.ToolStripMenuItem();
            this.openSTKTool = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.appExit = new System.Windows.Forms.ToolStripMenuItem();
            this.SANotify = new System.Windows.Forms.NotifyIcon(this.components);
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.dgvResult = new System.Windows.Forms.DataGridView();
            this.panel3 = new System.Windows.Forms.Panel();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnQuery = new System.Windows.Forms.Button();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.button1 = new System.Windows.Forms.Button();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.tbSiteInfo = new System.Windows.Forms.TextBox();
            this.btnSubmitData = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.btnDetailed = new System.Windows.Forms.Button();
            this.btnINTEdit = new System.Windows.Forms.Button();
            this.btnClear = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.btnStartWork = new System.Windows.Forms.Button();
            this.btnOpen = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.statusStrip1.SuspendLayout();
            this.menuDataOption.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.notifyMenu.SuspendLayout();
            this.tabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvResult)).BeginInit();
            this.panel3.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.panel2.SuspendLayout();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.panel1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.progressBarDisplay,
            this.percentageValue,
            this.descriptionText,
            this.toolSynTime,
            this.coordinateText});
            this.statusStrip1.Location = new System.Drawing.Point(0, 662);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(1, 0, 13, 0);
            this.statusStrip1.ShowItemToolTips = true;
            this.statusStrip1.Size = new System.Drawing.Size(1008, 39);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // progressBarDisplay
            // 
            this.progressBarDisplay.Name = "progressBarDisplay";
            this.progressBarDisplay.Size = new System.Drawing.Size(100, 31);
            // 
            // percentageValue
            // 
            this.percentageValue.Name = "percentageValue";
            this.percentageValue.Size = new System.Drawing.Size(31, 33);
            this.percentageValue.Text = "0%";
            // 
            // descriptionText
            // 
            this.descriptionText.AutoSize = false;
            this.descriptionText.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.descriptionText.Name = "descriptionText";
            this.descriptionText.Padding = new System.Windows.Forms.Padding(20, 0, 0, 0);
            this.descriptionText.Size = new System.Drawing.Size(500, 33);
            this.descriptionText.Text = "欢迎使用！";
            this.descriptionText.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // toolSynTime
            // 
            this.toolSynTime.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.toolSynTime.Name = "toolSynTime";
            this.toolSynTime.Size = new System.Drawing.Size(211, 33);
            this.toolSynTime.Spring = true;
            // 
            // coordinateText
            // 
            this.coordinateText.AutoSize = false;
            this.coordinateText.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.coordinateText.Name = "coordinateText";
            this.coordinateText.Size = new System.Drawing.Size(150, 33);
            this.coordinateText.Text = "0,0";
            this.coordinateText.ToolTipText = "坐标（经度，纬度）";
            // 
            // menuDataOption
            // 
            this.menuDataOption.Font = new System.Drawing.Font("宋体", 12F);
            this.menuDataOption.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuDataOption.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.exportExcel,
            this.exportErrExcel,
            this.mapLocation,
            this.myAnyalysis,
            this.tsmOperation});
            this.menuDataOption.Name = "menuDataOption";
            this.menuDataOption.Size = new System.Drawing.Size(378, 124);
            this.menuDataOption.Opening += new System.ComponentModel.CancelEventHandler(this.menuDataOption_Opening);
            // 
            // exportExcel
            // 
            this.exportExcel.Font = new System.Drawing.Font("宋体", 12F);
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.E)));
            this.exportExcel.Size = new System.Drawing.Size(377, 24);
            this.exportExcel.Text = "导出数据到Excel";
            this.exportExcel.Click += new System.EventHandler(this.btnExcel_Click);
            // 
            // exportErrExcel
            // 
            this.exportErrExcel.Enabled = false;
            this.exportErrExcel.Name = "exportErrExcel";
            this.exportErrExcel.ShortcutKeys = ((System.Windows.Forms.Keys)(((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.Alt) 
            | System.Windows.Forms.Keys.E)));
            this.exportErrExcel.Size = new System.Drawing.Size(377, 24);
            this.exportErrExcel.Text = "导出异常数据到Excel";
            this.exportErrExcel.Click += new System.EventHandler(this.exportErrExcel_Click);
            // 
            // mapLocation
            // 
            this.mapLocation.Font = new System.Drawing.Font("宋体", 12F);
            this.mapLocation.Name = "mapLocation";
            this.mapLocation.Size = new System.Drawing.Size(377, 24);
            this.mapLocation.Text = "地图上位置";
            this.mapLocation.Visible = false;
            this.mapLocation.Click += new System.EventHandler(this.mapLocation_Click);
            // 
            // myAnyalysis
            // 
            this.myAnyalysis.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.calSTKElevation,
            this.STKAnalysis,
            this.lineChart});
            this.myAnyalysis.Name = "myAnyalysis";
            this.myAnyalysis.Size = new System.Drawing.Size(377, 24);
            this.myAnyalysis.Text = "分析";
            this.myAnyalysis.Visible = false;
            // 
            // calSTKElevation
            // 
            this.calSTKElevation.Font = new System.Drawing.Font("宋体", 12F);
            this.calSTKElevation.Name = "calSTKElevation";
            this.calSTKElevation.Size = new System.Drawing.Size(271, 26);
            this.calSTKElevation.Text = "推测STK仰角";
            this.calSTKElevation.Visible = false;
            this.calSTKElevation.Click += new System.EventHandler(this.calSTKElevation_Click);
            // 
            // STKAnalysis
            // 
            this.STKAnalysis.Name = "STKAnalysis";
            this.STKAnalysis.ShortcutKeys = System.Windows.Forms.Keys.F2;
            this.STKAnalysis.Size = new System.Drawing.Size(271, 26);
            this.STKAnalysis.Text = "当前信号STK仿真";
            this.STKAnalysis.Click += new System.EventHandler(this.STKAnalysisFromSelect_Click);
            // 
            // lineChart
            // 
            this.lineChart.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lineChart.Name = "lineChart";
            this.lineChart.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.Q)));
            this.lineChart.Size = new System.Drawing.Size(271, 26);
            this.lineChart.Text = "图表分析";
            this.lineChart.Click += new System.EventHandler(this.lineChart_Click);
            // 
            // tsmOperation
            // 
            this.tsmOperation.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.myMark,
            this.tsmMyDel});
            this.tsmOperation.Name = "tsmOperation";
            this.tsmOperation.Size = new System.Drawing.Size(377, 24);
            this.tsmOperation.Text = "操作";
            this.tsmOperation.Visible = false;
            // 
            // myMark
            // 
            this.myMark.Image = ((System.Drawing.Image)(resources.GetObject("myMark.Image")));
            this.myMark.Name = "myMark";
            this.myMark.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.M)));
            this.myMark.Size = new System.Drawing.Size(201, 26);
            this.myMark.Text = "合批";
            this.myMark.Click += new System.EventHandler(this.myMark_Click);
            // 
            // tsmMyDel
            // 
            this.tsmMyDel.Name = "tsmMyDel";
            this.tsmMyDel.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D)));
            this.tsmMyDel.Size = new System.Drawing.Size(201, 26);
            this.tsmMyDel.Text = "移出";
            this.tsmMyDel.Click += new System.EventHandler(this.tsmMyDel_Click);
            // 
            // tabPage1
            // 
            this.tabPage1.BackColor = System.Drawing.Color.Black;
            this.tabPage1.Controls.Add(this.textBox2);
            this.tabPage1.Cursor = System.Windows.Forms.Cursors.SizeAll;
            this.tabPage1.Location = new System.Drawing.Point(4, 26);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(0);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(1000, 559);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "态势显示";
            this.tabPage1.Resize += new System.EventHandler(this.tabPage1_Resize);
            // 
            // textBox2
            // 
            this.textBox2.Dock = System.Windows.Forms.DockStyle.Right;
            this.textBox2.Location = new System.Drawing.Point(800, 0);
            this.textBox2.Multiline = true;
            this.textBox2.Name = "textBox2";
            this.textBox2.ReadOnly = true;
            this.textBox2.Size = new System.Drawing.Size(200, 559);
            this.textBox2.TabIndex = 3;
            // 
            // toolTip1
            // 
            this.toolTip1.IsBalloon = true;
            // 
            // appLog
            // 
            this.appLog.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.appLog.Location = new System.Drawing.Point(0, 522);
            this.appLog.Margin = new System.Windows.Forms.Padding(4);
            this.appLog.Multiline = true;
            this.appLog.Name = "appLog";
            this.appLog.ReadOnly = true;
            this.appLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.appLog.Size = new System.Drawing.Size(1008, 140);
            this.appLog.TabIndex = 3;
            // 
            // colorDialog1
            // 
            this.colorDialog1.AnyColor = true;
            this.colorDialog1.FullOpen = true;
            this.colorDialog1.SolidColorOnly = true;
            // 
            // toolStrip1
            // 
            this.toolStrip1.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.toolStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripDropDownButton1,
            this.toolStripDropDownButton2});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1008, 27);
            this.toolStrip1.TabIndex = 4;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripDropDownButton1
            // 
            this.toolStripDropDownButton1.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripDropDownButton1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmSystemSetting,
            this.tsmParamEdit});
            this.toolStripDropDownButton1.Image = ((System.Drawing.Image)(resources.GetObject("toolStripDropDownButton1.Image")));
            this.toolStripDropDownButton1.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripDropDownButton1.Name = "toolStripDropDownButton1";
            this.toolStripDropDownButton1.Size = new System.Drawing.Size(91, 24);
            this.toolStripDropDownButton1.Text = "系统设置";
            this.toolStripDropDownButton1.ToolTipText = "系统设置";
            // 
            // tsmSystemSetting
            // 
            this.tsmSystemSetting.Name = "tsmSystemSetting";
            this.tsmSystemSetting.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Alt | System.Windows.Forms.Keys.S)));
            this.tsmSystemSetting.Size = new System.Drawing.Size(244, 26);
            this.tsmSystemSetting.Text = "系统设置";
            this.tsmSystemSetting.Click += new System.EventHandler(this.tsmSystemSetting_Click);
            // 
            // tsmParamEdit
            // 
            this.tsmParamEdit.Name = "tsmParamEdit";
            this.tsmParamEdit.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Alt | System.Windows.Forms.Keys.E)));
            this.tsmParamEdit.Size = new System.Drawing.Size(244, 26);
            this.tsmParamEdit.Text = "雷达参数编辑";
            this.tsmParamEdit.Click += new System.EventHandler(this.tsmParamEdit_Click);
            // 
            // toolStripDropDownButton2
            // 
            this.toolStripDropDownButton2.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripDropDownButton2.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmOpenOrbit,
            this.tsmOpenSTKTool,
            this.toolStripMenuItem2});
            this.toolStripDropDownButton2.Image = ((System.Drawing.Image)(resources.GetObject("toolStripDropDownButton2.Image")));
            this.toolStripDropDownButton2.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripDropDownButton2.Name = "toolStripDropDownButton2";
            this.toolStripDropDownButton2.Size = new System.Drawing.Size(91, 24);
            this.toolStripDropDownButton2.Text = "其他功能";
            // 
            // tsmOpenOrbit
            // 
            this.tsmOpenOrbit.Name = "tsmOpenOrbit";
            this.tsmOpenOrbit.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Alt | System.Windows.Forms.Keys.P)));
            this.tsmOpenOrbit.Size = new System.Drawing.Size(262, 26);
            this.tsmOpenOrbit.Text = "卫星轨道预报";
            this.tsmOpenOrbit.Click += new System.EventHandler(this.tsmOpenOrbit_Click);
            // 
            // tsmOpenSTKTool
            // 
            this.tsmOpenSTKTool.Name = "tsmOpenSTKTool";
            this.tsmOpenSTKTool.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Alt | System.Windows.Forms.Keys.V)));
            this.tsmOpenSTKTool.Size = new System.Drawing.Size(262, 26);
            this.tsmOpenSTKTool.Text = "STK模拟器";
            this.tsmOpenSTKTool.Click += new System.EventHandler(this.tsmOpenSTKTool_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(262, 26);
            this.toolStripMenuItem2.Text = "SAR雷达数据分析工具";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // notifyMenu
            // 
            this.notifyMenu.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.notifyMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.showMain,
            this.toolStripSeparator1,
            this.other,
            this.toolStripSeparator2,
            this.appExit});
            this.notifyMenu.Name = "notifyMenu";
            this.notifyMenu.Size = new System.Drawing.Size(154, 88);
            // 
            // showMain
            // 
            this.showMain.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.showMain.Name = "showMain";
            this.showMain.Size = new System.Drawing.Size(153, 24);
            this.showMain.Text = "显示主界面";
            this.showMain.Click += new System.EventHandler(this.showMain_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(150, 6);
            // 
            // other
            // 
            this.other.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.openOrbit,
            this.openSTKTool});
            this.other.Name = "other";
            this.other.Size = new System.Drawing.Size(153, 24);
            this.other.Text = "其他功能";
            // 
            // openOrbit
            // 
            this.openOrbit.Name = "openOrbit";
            this.openOrbit.Size = new System.Drawing.Size(182, 26);
            this.openOrbit.Text = "卫星轨道预报";
            this.openOrbit.Click += new System.EventHandler(this.tsmOpenOrbit_Click);
            // 
            // openSTKTool
            // 
            this.openSTKTool.Name = "openSTKTool";
            this.openSTKTool.Size = new System.Drawing.Size(182, 26);
            this.openSTKTool.Text = "STK模拟仿真";
            this.openSTKTool.Click += new System.EventHandler(this.tsmOpenSTKTool_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(150, 6);
            // 
            // appExit
            // 
            this.appExit.Name = "appExit";
            this.appExit.Size = new System.Drawing.Size(153, 24);
            this.appExit.Text = "退出";
            this.appExit.Click += new System.EventHandler(this.appExit_Click);
            // 
            // SANotify
            // 
            this.SANotify.ContextMenuStrip = this.notifyMenu;
            this.SANotify.Icon = ((System.Drawing.Icon)(resources.GetObject("SANotify.Icon")));
            this.SANotify.Text = "雷达信号分析辅助系统";
            this.SANotify.Visible = true;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.dgvResult);
            this.tabPage4.Controls.Add(this.panel3);
            this.tabPage4.Location = new System.Drawing.Point(4, 28);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage4.Size = new System.Drawing.Size(1000, 465);
            this.tabPage4.TabIndex = 4;
            this.tabPage4.Text = "数据查询";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // dgvResult
            // 
            this.dgvResult.AllowUserToAddRows = false;
            this.dgvResult.AllowUserToDeleteRows = false;
            this.dgvResult.AllowUserToResizeColumns = false;
            this.dgvResult.AllowUserToResizeRows = false;
            this.dgvResult.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCells;
            this.dgvResult.ColumnHeadersHeight = 38;
            this.dgvResult.ContextMenuStrip = this.menuDataOption;
            this.dgvResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvResult.Location = new System.Drawing.Point(3, 66);
            this.dgvResult.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgvResult.MultiSelect = false;
            this.dgvResult.Name = "dgvResult";
            this.dgvResult.ReadOnly = true;
            this.dgvResult.RowHeadersWidth = 51;
            this.dgvResult.RowTemplate.Height = 27;
            this.dgvResult.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvResult.Size = new System.Drawing.Size(994, 396);
            this.dgvResult.TabIndex = 4;
            this.dgvResult.VirtualMode = true;
            this.dgvResult.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.dataGridView1_RowPostPaint);
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.label3);
            this.panel3.Controls.Add(this.label4);
            this.panel3.Controls.Add(this.btnQuery);
            this.panel3.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel3.Location = new System.Drawing.Point(3, 3);
            this.panel3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(994, 63);
            this.panel3.TabIndex = 5;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.Color.Transparent;
            this.label3.Location = new System.Drawing.Point(21, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(104, 19);
            this.label3.TabIndex = 14;
            this.label3.Text = "开始时间：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.BackColor = System.Drawing.Color.Transparent;
            this.label4.Location = new System.Drawing.Point(380, 24);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(104, 19);
            this.label4.TabIndex = 13;
            this.label4.Text = "结束时间：";
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQuery.Font = new System.Drawing.Font("宋体", 12F);
            this.btnQuery.Location = new System.Drawing.Point(872, 12);
            this.btnQuery.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(100, 38);
            this.btnQuery.TabIndex = 0;
            this.btnQuery.Text = "查  询";
            this.btnQuery.UseVisualStyleBackColor = false;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.flowLayoutPanel1);
            this.tabPage3.Controls.Add(this.panel2);
            this.tabPage3.Location = new System.Drawing.Point(4, 28);
            this.tabPage3.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage3.Size = new System.Drawing.Size(1000, 465);
            this.tabPage3.TabIndex = 3;
            this.tabPage3.Text = "图表分析";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.AutoScroll = true;
            this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(204, 4);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.Size = new System.Drawing.Size(792, 457);
            this.flowLayoutPanel1.TabIndex = 1;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.WhiteSmoke;
            this.panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel2.Controls.Add(this.button1);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Left;
            this.panel2.Location = new System.Drawing.Point(4, 4);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(200, 457);
            this.panel2.TabIndex = 1;
            // 
            // button1
            // 
            this.button1.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.button1.Location = new System.Drawing.Point(17, 13);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(160, 36);
            this.button1.TabIndex = 0;
            this.button1.Text = "时序分析";
            this.button1.UseVisualStyleBackColor = true;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.dataGridView1);
            this.tabPage2.Controls.Add(this.panel1);
            this.tabPage2.Location = new System.Drawing.Point(4, 28);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Size = new System.Drawing.Size(1000, 463);
            this.tabPage2.TabIndex = 2;
            this.tabPage2.Text = "数据分选";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.AllowUserToResizeRows = false;
            this.dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.dataGridView1.ColumnHeadersHeight = 38;
            this.dataGridView1.ContextMenuStrip = this.menuDataOption;
            this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView1.Location = new System.Drawing.Point(3, 108);
            this.dataGridView1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.RowHeadersWidth = 51;
            this.dataGridView1.RowTemplate.Height = 27;
            this.dataGridView1.Size = new System.Drawing.Size(994, 353);
            this.dataGridView1.TabIndex = 0;
            this.dataGridView1.CellMouseDown += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dataGridView1_CellMouseDown);
            this.dataGridView1.CellMouseUp += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dataGridView1_CellMouseUp);
            this.dataGridView1.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.dataGridView1_RowPostPaint);
            this.dataGridView1.SelectionChanged += new System.EventHandler(this.dataGridView1_SelectionChanged);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.tbSiteInfo);
            this.panel1.Controls.Add(this.btnSubmitData);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.btnDetailed);
            this.panel1.Controls.Add(this.btnINTEdit);
            this.panel1.Controls.Add(this.btnClear);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.btnStartWork);
            this.panel1.Controls.Add(this.btnOpen);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(3, 2);
            this.panel1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(994, 106);
            this.panel1.TabIndex = 3;
            // 
            // tbSiteInfo
            // 
            this.tbSiteInfo.Font = new System.Drawing.Font("宋体", 12F);
            this.tbSiteInfo.Location = new System.Drawing.Point(110, 62);
            this.tbSiteInfo.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbSiteInfo.MaxLength = 8;
            this.tbSiteInfo.Name = "tbSiteInfo";
            this.tbSiteInfo.ReadOnly = true;
            this.tbSiteInfo.Size = new System.Drawing.Size(320, 30);
            this.tbSiteInfo.TabIndex = 0;
            this.tbSiteInfo.Text = "未设置";
            // 
            // btnSubmitData
            // 
            this.btnSubmitData.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSubmitData.Font = new System.Drawing.Font("宋体", 12F);
            this.btnSubmitData.Location = new System.Drawing.Point(859, 56);
            this.btnSubmitData.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnSubmitData.Name = "btnSubmitData";
            this.btnSubmitData.Size = new System.Drawing.Size(120, 38);
            this.btnSubmitData.TabIndex = 0;
            this.btnSubmitData.Text = "5.提交数据";
            this.btnSubmitData.UseVisualStyleBackColor = true;
            this.btnSubmitData.Click += new System.EventHandler(this.btnSubmitData_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.BackColor = System.Drawing.Color.Transparent;
            this.label5.Location = new System.Drawing.Point(21, 68);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(104, 19);
            this.label5.TabIndex = 13;
            this.label5.Text = "站点信息：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.Location = new System.Drawing.Point(21, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(104, 19);
            this.label1.TabIndex = 14;
            this.label1.Text = "开始时间：";
            // 
            // btnDetailed
            // 
            this.btnDetailed.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDetailed.Font = new System.Drawing.Font("宋体", 12F);
            this.btnDetailed.Location = new System.Drawing.Point(733, 56);
            this.btnDetailed.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnDetailed.Name = "btnDetailed";
            this.btnDetailed.Size = new System.Drawing.Size(120, 38);
            this.btnDetailed.TabIndex = 7;
            this.btnDetailed.Text = "3.完整数据";
            this.btnDetailed.UseVisualStyleBackColor = true;
            this.btnDetailed.Click += new System.EventHandler(this.btnDetailed_Click);
            // 
            // btnINTEdit
            // 
            this.btnINTEdit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnINTEdit.BackColor = System.Drawing.Color.LimeGreen;
            this.btnINTEdit.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnINTEdit.Font = new System.Drawing.Font("宋体", 12F);
            this.btnINTEdit.ForeColor = System.Drawing.Color.White;
            this.btnINTEdit.Location = new System.Drawing.Point(463, 56);
            this.btnINTEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnINTEdit.Name = "btnINTEdit";
            this.btnINTEdit.Size = new System.Drawing.Size(138, 38);
            this.btnINTEdit.TabIndex = 0;
            this.btnINTEdit.Text = "开启数据编辑";
            this.btnINTEdit.UseVisualStyleBackColor = false;
            this.btnINTEdit.Click += new System.EventHandler(this.btnINTEdit_Click);
            // 
            // btnClear
            // 
            this.btnClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnClear.Font = new System.Drawing.Font("宋体", 12F);
            this.btnClear.Location = new System.Drawing.Point(733, 11);
            this.btnClear.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(120, 38);
            this.btnClear.TabIndex = 8;
            this.btnClear.Text = "0.清除所有";
            this.btnClear.UseVisualStyleBackColor = true;
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.BackColor = System.Drawing.Color.Transparent;
            this.label2.Location = new System.Drawing.Point(380, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(104, 19);
            this.label2.TabIndex = 13;
            this.label2.Text = "结束时间：";
            // 
            // btnStartWork
            // 
            this.btnStartWork.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnStartWork.BackColor = System.Drawing.Color.Orange;
            this.btnStartWork.Font = new System.Drawing.Font("宋体", 12F);
            this.btnStartWork.Location = new System.Drawing.Point(607, 56);
            this.btnStartWork.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnStartWork.Name = "btnStartWork";
            this.btnStartWork.Size = new System.Drawing.Size(120, 38);
            this.btnStartWork.TabIndex = 6;
            this.btnStartWork.Text = "2.开始分选";
            this.btnStartWork.UseVisualStyleBackColor = false;
            this.btnStartWork.Click += new System.EventHandler(this.btnStartWork_Click);
            // 
            // btnOpen
            // 
            this.btnOpen.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOpen.Font = new System.Drawing.Font("宋体", 12F);
            this.btnOpen.Location = new System.Drawing.Point(859, 11);
            this.btnOpen.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnOpen.Name = "btnOpen";
            this.btnOpen.Size = new System.Drawing.Size(120, 38);
            this.btnOpen.TabIndex = 0;
            this.btnOpen.Text = "1.载入数据";
            this.btnOpen.UseVisualStyleBackColor = false;
            this.btnOpen.Click += new System.EventHandler(this.btnOpen_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 27);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1008, 495);
            this.tabControl1.TabIndex = 1;
            // 
            // DataAnalysis
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 701);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.appLog);
            this.Controls.Add(this.statusStrip1);
            this.Font = new System.Drawing.Font("宋体", 11F);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.MinimumSize = new System.Drawing.Size(1024, 739);
            this.Name = "DataAnalysis";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "雷达信号分析";
            this.Activated += new System.EventHandler(this.DataAnalysis_Activated);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Main_FormClosing);
            this.Load += new System.EventHandler(this.Main_Load);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.menuDataOption.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.notifyMenu.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvResult)).EndInit();
            this.panel3.ResumeLayout(false);
            this.panel3.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripProgressBar progressBarDisplay;
        private System.Windows.Forms.ToolStripStatusLabel percentageValue;
        private System.Windows.Forms.ToolStripStatusLabel descriptionText;
        private System.Windows.Forms.ContextMenuStrip menuDataOption;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.ToolStripMenuItem mapLocation;
        private System.Windows.Forms.ToolStripStatusLabel toolSynTime;
        private System.Windows.Forms.ToolStripStatusLabel coordinateText;
        private SignalAnalyzer.Controls.PanelEnhanced mapPanel;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.TextBox appLog;
        private System.Windows.Forms.ToolStripMenuItem myAnyalysis;
        private System.Windows.Forms.ToolStripMenuItem calSTKElevation;
        private System.Windows.Forms.ToolStripMenuItem STKAnalysis;
        private System.Windows.Forms.ToolStripMenuItem lineChart;
        private System.Windows.Forms.ToolStripMenuItem myMark;
        private System.Windows.Forms.ColorDialog colorDialog1;
        private System.Windows.Forms.ToolStripMenuItem exportErrExcel;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripDropDownButton toolStripDropDownButton1;
        private System.Windows.Forms.ToolStripMenuItem tsmSystemSetting;
        private System.Windows.Forms.ToolStripDropDownButton toolStripDropDownButton2;
        private System.Windows.Forms.ToolStripMenuItem tsmParamEdit;
        private System.Windows.Forms.ToolStripMenuItem tsmOpenOrbit;
        private System.Windows.Forms.ToolStripMenuItem tsmOpenSTKTool;
        private System.Windows.Forms.ContextMenuStrip notifyMenu;
        private System.Windows.Forms.ToolStripMenuItem showMain;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem appExit;
        private System.Windows.Forms.NotifyIcon SANotify;
        private System.Windows.Forms.ToolStripMenuItem tsmOperation;
        private System.Windows.Forms.ToolStripMenuItem tsmMyDel;
        private System.Windows.Forms.ToolStripMenuItem other;
        private System.Windows.Forms.ToolStripMenuItem openOrbit;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem openSTKTool;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.DataGridView dgvResult;
        private System.Windows.Forms.Panel panel3;
        private Controls.TimeInputBox timeUp;
        private Controls.TimeInputBox timeDown;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnQuery;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart1;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart2;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart3;
        private System.Windows.Forms.DataVisualization.Charting.Chart chart4;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.Panel panel1;
        private Controls.TimeInputBox timeStart;
        private Controls.TimeInputBox timeEnd;
        private System.Windows.Forms.TextBox tbSiteInfo;
        private System.Windows.Forms.Button btnSubmitData;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnDetailed;
        private System.Windows.Forms.Button btnINTEdit;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnStartWork;
        private System.Windows.Forms.Button btnOpen;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
    }
}

