<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5597B644-386B-4C10-9643-C0264824F867}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SignalAnalyzer</RootNamespace>
    <AssemblyName>SignalAnalyzer</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>icon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CommonUtils">
      <HintPath>..\..\V1.3 - Copy - 副本 -123 - 副本\SignalAnalyzer.Common\bin\Debug\CommonUtils.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net40\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="GeoEngine, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\OrbitPredictor\bin\Debug\GeoEngine.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.2.2\lib\net45\NLog.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="SignalAnalyzer.Common, Version=1.1.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\V1.3 - Copy - 副本 -123 - 副本\SignalAnalyzer\bin\Debug\SignalAnalyzer.Common.dll</HintPath>
    </Reference>
    <Reference Include="SignalAnalyzer.DBUtils, Version=1.1.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\SignalAnalyzer.DBUtils.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SQLite, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\lib\net40\System.Data.SQLite.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.SQLite.EF6, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SQLite.EF6.1.0.118.0\lib\net40\System.Data.SQLite.EF6.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.SQLite.Linq, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SQLite.Linq.1.0.118.0\lib\net40\System.Data.SQLite.Linq.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">

      <HintPath>..\packages\Microsoft.Office.Interop.Excel.15.0.4795.1001\lib\net20\Microsoft.Office.Interop.Excel.dll</HintPath>

      <EmbedInteropTypes>True</EmbedInteropTypes>

    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DatabaseManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DatabaseManager.Designer.cs">
      <DependentUpon>DatabaseManager.cs</DependentUpon>
    </Compile>
    <Compile Include="DataFactory\RCL673_3G.cs" />
    <Compile Include="DataFactory\RCL673MDB.cs" />
    <Compile Include="DataFactory\RCL673_5L.cs" />
    <Compile Include="EditParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EditParam.Designer.cs">
      <DependentUpon>EditParam.cs</DependentUpon>
    </Compile>
    <Compile Include="EditInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EditInfo.Designer.cs">
      <DependentUpon>EditInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="AddParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddParam.Designer.cs">
      <DependentUpon>AddParam.cs</DependentUpon>
    </Compile>
    <Compile Include="AddInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddInfo.Designer.cs">
      <DependentUpon>AddInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="AppContext.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Functions\ExcelExport.cs" />
    <Compile Include="Functions\MyCharts.cs" />
    <Compile Include="Objects\RadarSatellite.cs" />
    <Compile Include="Objects\RadarSignalCollect.cs" />
    <Compile Include="Objects\RadarSignalSubmit.cs" />
    <Compile Include="Objects\SiteEquator.cs" />
    <Compile Include="SelectSatellite.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SelectSatellite.Designer.cs">
      <DependentUpon>SelectSatellite.cs</DependentUpon>
    </Compile>
    <Compile Include="Utilities\SQLiteHelper.cs" />
    <Compile Include="Objects\SatelliteBase.cs" />
    <Compile Include="Objects\SatelliteParam.cs" />
    <Compile Include="RadarEdits.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RadarEdits.Designer.cs">
      <DependentUpon>RadarEdits.cs</DependentUpon>
    </Compile>
    <Compile Include="DataFactory\DBSource.cs" />
    <Compile Include="DataFactory\RCL673_4G.cs" />
    <Compile Include="DataFactory\RCL673_4.cs" />
    <Compile Include="ResourcesRegulator.cs" />
    <Compile Include="SelectDataType.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SelectDataType.Designer.cs">
      <DependentUpon>SelectDataType.cs</DependentUpon>
    </Compile>
    <Compile Include="SelectTleObject.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SelectTleObject.Designer.cs">
      <DependentUpon>SelectTleObject.cs</DependentUpon>
    </Compile>
    <Compile Include="Functions\SignalSelectionImpl.cs" />
    <Compile Include="Utilities\AsyncTask.cs" />
    <Compile Include="Utilities\Digitizer.cs" />
    <Compile Include="Utilities\Extensions.cs" />
    <Compile Include="Utilities\LocalTimeChange.cs" />
    <Compile Include="Utilities\NLogHepler.cs" />
    <Compile Include="Utilities\UlidGenerator.cs" />
    <Compile Include="Utilities\XmlHelper.cs" />
    <Compile Include="Objects\SatelliteInfo.cs" />
    <Compile Include="Functions\OrbitRelatedImpl.cs" />
    <Compile Include="ShowElevation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShowElevation.Designer.cs">
      <DependentUpon>ShowElevation.cs</DependentUpon>
    </Compile>
    <Compile Include="DataAnalysis.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DataAnalysis.Designer.cs">
      <DependentUpon>DataAnalysis.cs</DependentUpon>
    </Compile>
    <Compile Include="Objects\OrbitDataView.cs" />
    <Compile Include="Objects\RaderSignalCapture.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Objects\SatelliteCast.cs" />
    <Compile Include="Objects\SignalExcel.cs" />
    <Compile Include="Objects\RadarSignal.cs" />
    <Compile Include="SystemSetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting.Designer.cs">
      <DependentUpon>SystemSetting.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="DatabaseManager.resx">
      <DependentUpon>DatabaseManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EditParam.resx">
      <DependentUpon>EditParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="EditInfo.resx">
      <DependentUpon>EditInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AddParam.resx">
      <DependentUpon>AddParam.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="AddInfo.resx">
      <DependentUpon>AddInfo.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RadarEdits.resx">
      <DependentUpon>RadarEdits.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SelectDataType.resx">
      <DependentUpon>SelectDataType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SelectSatellite.resx">
      <DependentUpon>SelectSatellite.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SelectTleObject.resx">
      <DependentUpon>SelectTleObject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShowElevation.resx">
      <DependentUpon>ShowElevation.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DataAnalysis.resx">
      <DependentUpon>DataAnalysis.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="SystemSetting.resx">
      <DependentUpon>SystemSetting.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="JRO">
      <Guid>{AC3B8B4C-B6CA-11D1-9F31-00C04FC29D52}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>6</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <Private>True</Private>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\IRemoteContect\IRemoteContect.csproj">
      <Project>{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}</Project>
      <Name>IRemoteContect</Name>
    </ProjectReference>
    <ProjectReference Include="..\OrbitPredictor\OrbitPredictor.csproj">
      <Project>{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}</Project>
      <Name>OrbitPredictor</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="icon.ico" />
    <Content Include="Resources\GDMap_05_A.jpg" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net40\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net40\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net40\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net40\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>