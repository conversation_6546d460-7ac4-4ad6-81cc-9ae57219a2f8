
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.23107.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SignalAnalyzer", "SignalAnalyzer\SignalAnalyzer.csproj", "{5597B644-386B-4C10-9643-C0264824F867}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SignalAnalyzer.DBUtils", "SignalAnalyzer.DBUtils\SignalAnalyzer.DBUtils.csproj", "{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrbitPredictor", "OrbitPredictor\OrbitPredictor.csproj", "{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SignalAnalyzer.Common", "SignalAnalyzer.Common\SignalAnalyzer.Common.csproj", "{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IRemoteContect", "IRemoteContect\IRemoteContect.csproj", "{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5597B644-386B-4C10-9643-C0264824F867}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5597B644-386B-4C10-9643-C0264824F867}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5597B644-386B-4C10-9643-C0264824F867}.Debug|x86.ActiveCfg = Debug|x86
		{5597B644-386B-4C10-9643-C0264824F867}.Debug|x86.Build.0 = Debug|x86
		{5597B644-386B-4C10-9643-C0264824F867}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5597B644-386B-4C10-9643-C0264824F867}.Release|Any CPU.Build.0 = Release|Any CPU
		{5597B644-386B-4C10-9643-C0264824F867}.Release|x86.ActiveCfg = Release|x86
		{5597B644-386B-4C10-9643-C0264824F867}.Release|x86.Build.0 = Release|x86
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Debug|x86.Build.0 = Debug|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Release|x86.ActiveCfg = Release|Any CPU
		{1A7F9A6B-F53F-4228-974A-15C5DF9114DD}.Release|x86.Build.0 = Release|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Debug|x86.Build.0 = Debug|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Release|x86.ActiveCfg = Release|Any CPU
		{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}.Release|x86.Build.0 = Release|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Debug|x86.Build.0 = Debug|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Release|x86.ActiveCfg = Release|Any CPU
		{E1FCBF9B-B2AE-4A8E-AF98-5FEE4C8ECF80}.Release|x86.Build.0 = Release|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Debug|x86.Build.0 = Debug|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Release|x86.ActiveCfg = Release|Any CPU
		{8F237B5B-2836-4E11-B6F3-4BC13F0133C2}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
	EndGlobalSection
EndGlobal
