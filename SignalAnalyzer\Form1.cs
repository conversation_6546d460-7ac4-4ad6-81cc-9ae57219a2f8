﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SignalAnalyzer
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
            Load += MainForm_Load;
            FormClosing += MainForm_FormClosing;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // 初始化UI
            InitializeUI();

            // 加载配置
            LoadConfig();
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 保存配置
            SaveConfig();
        }

        private void InitializeUI()
        {
            // 设置窗体属性
            Text = "SAR雷达数据分析工具";
            Width = 1200;
            Height = 800;
            StartPosition = FormStartPosition.CenterScreen;
            BackColor = Color.FromArgb(248, 250, 252); // #F8FAFC
            Font = new Font("微软雅黑", 9F, FontStyle.Regular);

            // 主布局容器
            var mainContainer = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            Controls.Add(mainContainer);

            // 主布局
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 5,
                BackColor = Color.White,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));  // 文件选择区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 200)); // 卫星列表区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 70));  // 按钮区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));  // 结果显示区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));  // 导出区域
            mainContainer.Controls.Add(mainPanel);

            // 文件选择区域
            var filePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                BackColor = Color.White,
                Padding = new Padding(0, 10, 0, 10)
            };
            filePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 80));
            filePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20));
            filePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 25));
            filePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            mainPanel.Controls.Add(filePanel, 0, 0);

            var fileLabel = new Label
            {
                Text = "雷达轨道数据文件：",
                AutoSize = true,
                Font = new Font("微软雅黑", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(45, 55, 72),
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };
            filePanel.Controls.Add(fileLabel, 0, 0);

            var filePathTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(45, 55, 72),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(0, 0, 10, 0)
            };
            filePanel.Controls.Add(filePathTextBox, 0, 1);

            var browseButton = new Button
            {
                Text = "浏览文件",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(74, 144, 226),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            browseButton.FlatAppearance.BorderSize = 0;
            browseButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(53, 122, 189);
            browseButton.Click += (s, e) => BrowseFile(filePathTextBox);
            filePanel.Controls.Add(browseButton, 1, 1);

            // 卫星列表区域
            var satPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                BackColor = Color.White,
                Padding = new Padding(0, 15, 0, 15)
            };
            satPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            satPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 25));
            satPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            satPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 45));
            mainPanel.Controls.Add(satPanel, 0, 1);

            var satLabel = new Label
            {
                Text = "卫星编号列表 (每行一个或逗号分隔)：",
                AutoSize = true,
                Font = new Font("微软雅黑", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(45, 55, 72),
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };
            satPanel.Controls.Add(satLabel, 0, 0);

            var satListTextBox = new TextBox
            {
                Multiline = true,
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(45, 55, 72),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                ScrollBars = ScrollBars.Vertical,
                Margin = new Padding(0, 5, 0, 5)
            };
            satPanel.Controls.Add(satListTextBox, 0, 1);

            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false,
                BackColor = Color.White
            };
            satPanel.Controls.Add(buttonPanel, 0, 2);

            var importButton = new Button
            {
                Text = "导入列表",
                Size = new Size(100, 35),
                Font = new Font("微软雅黑", 9F, FontStyle.Regular),
                BackColor = Color.White,
                ForeColor = Color.FromArgb(45, 55, 72),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Margin = new Padding(0, 0, 10, 0)
            };
            importButton.FlatAppearance.BorderColor = Color.FromArgb(221, 230, 237);
            importButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(248, 250, 252);
            importButton.Click += (s, e) => ImportSatelliteList(satListTextBox);
            buttonPanel.Controls.Add(importButton);

            var clearButton = new Button
            {
                Text = "清空列表",
                Size = new Size(100, 35),
                Font = new Font("微软雅黑", 9F, FontStyle.Regular),
                BackColor = Color.White,
                ForeColor = Color.FromArgb(45, 55, 72),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            clearButton.FlatAppearance.BorderColor = Color.FromArgb(221, 230, 237);
            clearButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(248, 250, 252);
            clearButton.Click += (s, e) => ClearSatelliteList(satListTextBox);
            buttonPanel.Controls.Add(clearButton);

            // 按钮区域
            var actionButtonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false,
                BackColor = Color.White,
                Anchor = AnchorStyles.None
            };
            // 居中对齐
            actionButtonPanel.Anchor = AnchorStyles.None;
            mainPanel.Controls.Add(actionButtonPanel, 0, 2);

            var processButton = new Button
            {
                Text = "处理数据",
                Size = new Size(140, 40),
                Font = new Font("微软雅黑", 10F, FontStyle.Regular),
                BackColor = Color.FromArgb(74, 144, 226),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Margin = new Padding(0, 15, 15, 15)
            };
            processButton.FlatAppearance.BorderSize = 0;
            processButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(53, 122, 189);
            processButton.Click += (s, e) => ProcessData(filePathTextBox.Text, satListTextBox.Text);
            actionButtonPanel.Controls.Add(processButton);

            var reconButton = new Button
            {
                Text = "生成侦察数据",
                Size = new Size(140, 40),
                Font = new Font("微软雅黑", 10F, FontStyle.Regular),
                BackColor = Color.FromArgb(72, 187, 120),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Margin = new Padding(15, 15, 0, 15)
            };
            reconButton.FlatAppearance.BorderSize = 0;
            reconButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(56, 161, 105);
            reconButton.Click += (s, e) => GenerateReconData();
            actionButtonPanel.Controls.Add(reconButton);

            // 结果显示区域
            var resultPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(0, 15, 0, 15)
            };
            mainPanel.Controls.Add(resultPanel, 0, 3);

            var resultLabel = new Label
            {
                Text = "处理结果：",
                AutoSize = true,
                Font = new Font("微软雅黑", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(45, 55, 72),
                Location = new Point(0, 0)
            };
            resultPanel.Controls.Add(resultLabel);

            var resultTextBox = new TextBox
            {
                Multiline = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                ScrollBars = ScrollBars.Both,
                ReadOnly = true,
                Font = new Font("Consolas", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(45, 55, 72),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(0, 25),
                Size = new Size(resultPanel.Width, resultPanel.Height - 40)
            };
            resultPanel.Controls.Add(resultTextBox);

            // 导出按钮区域
            var exportPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(0, 10, 0, 0)
            };
            mainPanel.Controls.Add(exportPanel, 0, 4);

            var exportButton = new Button
            {
                Text = "导出结果",
                Size = new Size(120, 35),
                Font = new Font("微软雅黑", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(74, 144, 226),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            exportButton.FlatAppearance.BorderSize = 0;
            exportButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(53, 122, 189);
            exportButton.Location = new Point(exportPanel.Width - 120, 10);
            exportButton.Click += (s, e) => ExportResult(resultTextBox.Text);
            exportPanel.Controls.Add(exportButton);

            // 状态栏
            var statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(248, 250, 252),
                Font = new Font("微软雅黑", 9F, FontStyle.Regular)
            };
            var statusLabel = new ToolStripStatusLabel
            {
                Text = "就绪",
                ForeColor = Color.FromArgb(113, 128, 150)
            };
            statusStrip.Items.Add(statusLabel);
            Controls.Add(statusStrip);

            // 保存控件引用
            this.filePathTextBox = filePathTextBox;
            this.satListTextBox = satListTextBox;
            this.resultTextBox = resultTextBox;
            this.statusLabel = statusLabel;

            // 保存面板引用用于窗口调整
            var exportPanelRef = exportPanel;
            var exportButtonRef = exportButton;
            var resultPanelRef = resultPanel;
            var resultTextBoxRef = resultTextBox;

            // 设置窗口调整时的处理
            this.Resize += (s, e) => {
                // 确保导出按钮位置正确
                if (exportButtonRef != null && exportPanelRef != null)
                {
                    exportButtonRef.Location = new Point(exportPanelRef.Width - 120, 10);
                }
                // 确保结果文本框大小正确
                if (resultTextBoxRef != null && resultPanelRef != null)
                {
                    resultTextBoxRef.Size = new Size(resultPanelRef.Width, resultPanelRef.Height - 40);
                }
            };
        }


        // 卫星名称映射表
        private readonly Dictionary<string, string> nameMapping = new Dictionary<string, string>
      {
          {"未来成像体系雷达-1", "FIA-1"},
          {"未来成像体系雷达-2", "FIA-2"},
          {"未来成像体系雷达-3", "FIA-3"},
          {"未来成像体系雷达-4", "FIA-4"},
          {"未来成像体系雷达-5", "FIA-5"},
          {"信息收集卫星-R3", "IGS-7A"},
          {"信息收集卫星-R4", "IGS-8A"},
          {"信息收集卫星-R5", "IGS-R5"},
          {"信息收集卫星-R6", "IGS-R6"},
          {"信息收集卫星-R7", "IGS-R7"},
          {"信息收集卫星-R8", "IGS-R8"},
          {"信息收集卫星-RSpare", "IGS-RSpare"},
          {"地中海盆地卫星-1", "SKYMED-1"},
          {"地中海盆地卫星-2", "SKYMED-2"},
          {"地中海盆地卫星-3", "SKYMED-3"},
          {"地中海盆地卫星-4", "SKYMED-4"},
          {"第二代地中海盆地卫星-1", "CSG-1"},
          {"第二代地中海盆地卫星-2", "CSG-2"},
          {"SARah-1", "SARah-1"},
          {"合成孔径雷达-2", "SARah-2"},
          {"I2P-2（宇宙-252）碎片", "SARah-3"},
          {"QPS-SAR-9", "QPS-SAR-9"},
          {"本影-05", "UMBRA-05"},
          {"本影-07", "UMBRA-07"},
          {"本影-08", "UMBRA-08"},
          {"本影-09", "UMBRA-09"},
          {"本影-10", "UMBRA-10"}
      };

        // 轨道数据映射表
        private readonly Dictionary<string, string> orbitMapping = new Dictionary<string, string>
      {
          {"37162U", "10046A"},
          {"38109U", "12014A"},
          {"39462U", "13072A"},
          {"41334U", "16010A"},
          {"43145U", "18005A"},
          {"42072U", "17015A"},
          {"43495U", "18052A"},
          {"55329U", "23012A"},
          {"37954U", "11075A"},
          {"39061U", "13002A"},
          {"40381U", "15004A"},
          {"55036U", "23001AT"},
          {"58297U", "23174AT"},
          {"58292U", "23174AN"},
          {"60541U", "24149CB"},
          {"60547U", "24149CH"},
          {"31598U", "07023A"},
          {"32376U", "07059A"},
          {"33412U", "08054A"},
          {"37216U", "10060A"},
          {"44873U", "19092A"},
          {"51444U", "22008A"},
          {"52887U", "22063A"},
          {"58643U", "23204A"},
          {"58644U", "23240B"},
          {"63205U", "25050A"}
      };

        // 卫星列表
        private HashSet<string> satelliteList = new HashSet<string>();

        // 配置文件路径
        private readonly string configFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SARRadarAnalyzer", "config.txt");

        // 控件引用
        private TextBox filePathTextBox;
        private TextBox satListTextBox;
        private TextBox resultTextBox;
        private ToolStripStatusLabel statusLabel;

        private void BrowseFile(TextBox filePathTextBox)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*";
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    filePathTextBox.Text = openFileDialog.FileName;
                    statusLabel.Text = $"已选择文件: {openFileDialog.FileName}";
                }
            }
        }

        private void ImportSatelliteList(TextBox satListTextBox)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*";
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var content = File.ReadAllText(openFileDialog.FileName);
                        var sats = new HashSet<string>();

                        foreach (var line in content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries))
                        {
                            var trimmedLine = line.Trim();
                            if (!string.IsNullOrEmpty(trimmedLine))
                            {
                                foreach (var sat in trimmedLine.Split(','))
                                {
                                    var trimmedSat = sat.Trim();
                                    if (!string.IsNullOrEmpty(trimmedSat))
                                    {
                                        sats.Add(trimmedSat);
                                    }
                                }
                            }
                        }

                        if (sats.Count > 0)
                        {
                            satelliteList.UnionWith(sats);
                            satListTextBox.Text = string.Join(Environment.NewLine, satelliteList.OrderBy(x => x));
                            statusLabel.Text = $"已从文件导入 {sats.Count} 个卫星编号";
                        }
                        else
                        {
                            MessageBox.Show("文件中没有找到有效的卫星编号！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"导入卫星列表时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ClearSatelliteList(TextBox satListTextBox)
        {
            if (MessageBox.Show("确定要清空卫星列表吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                satelliteList.Clear();
                satListTextBox.Clear();
                statusLabel.Text = "卫星列表已清空";
            }
        }

        private void ProcessData(string filePath, string satListText)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                MessageBox.Show("请先选择数据文件！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 更新卫星列表
            satelliteList = new HashSet<string>();
            foreach (var line in satListText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries))
            {
                var trimmedLine = line.Trim();
                if (!string.IsNullOrEmpty(trimmedLine))
                {
                    foreach (var sat in trimmedLine.Split(','))
                    {
                        var trimmedSat = sat.Trim();
                        if (!string.IsNullOrEmpty(trimmedSat))
                        {
                            satelliteList.Add(trimmedSat);
                        }
                    }
                }
            }

            if (satelliteList.Count == 0)
            {
                MessageBox.Show("卫星列表为空，请先添加卫星编号！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var rawData = File.ReadAllLines(filePath);
                var processedData = new List<string>();

                foreach (var line in rawData)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine)) continue;

                    var parts = trimmedLine.Split('|');
                    if (parts.Length < 15) continue;

                    var satId = parts[1].Trim();
                    if (satelliteList.Contains(satId))
                    {
                        var name = parts[4].Trim();
                        var dataPart = string.Join(Environment.NewLine, parts.Skip(14));
                        processedData.Add($"{name}{Environment.NewLine}{dataPart}");
                    }
                }

                resultTextBox.Text = string.Join($"{Environment.NewLine}{Environment.NewLine}", processedData);
                statusLabel.Text = $"处理完成，找到 {processedData.Count} 条匹配数据";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理文件时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "处理失败";
            }
        }

        private void GenerateReconData()
        {
            var currentData = resultTextBox.Text;
            if (string.IsNullOrEmpty(currentData))
            {
                MessageBox.Show("没有可处理的数据！请先处理数据。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var entries = currentData.Split(new[] { "\r\n\r\n", "\n\n" }, StringSplitOptions.RemoveEmptyEntries);
                var processedEntries = new List<string>();

                foreach (var entry in entries)
                {
                    if (string.IsNullOrWhiteSpace(entry)) continue;

                    var lines = entry.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    if (lines.Length < 2)
                    {
                        processedEntries.Add(entry);
                        continue;
                    }

                    // 处理名称行
                    var originalName = lines[0].Trim();
                    var newName = nameMapping.TryGetValue(originalName, out var mappedName) ? mappedName : originalName;

                    // 处理轨道数据行
                    var processedLines = new List<string> { newName };

                    foreach (var line in lines.Skip(1))
                    {
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            processedLines.Add(line);
                            continue;
                        }

                        // 检查并替换轨道数据
                        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        for (int i = 0; i < parts.Length - 1; i++)
                        {
                            if (orbitMapping.TryGetValue(parts[i], out var mappedOrbit))
                            {
                                parts[i + 1] = mappedOrbit;
                            }
                        }

                        processedLines.Add(string.Join(" ", parts));
                    }

                    processedEntries.Add(string.Join(Environment.NewLine, processedLines));
                }

                resultTextBox.Text = string.Join($"{Environment.NewLine}{Environment.NewLine}", processedEntries);
                statusLabel.Text = "侦察数据生成完成";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成侦察数据时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "侦察数据生成失败";
            }
        }

        private void ExportResult(string resultText)
        {
            if (string.IsNullOrEmpty(resultText))
            {
                MessageBox.Show("没有可导出的数据！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*";
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var filePath = saveFileDialog.FileName;
                        if (!filePath.EndsWith(".txt", StringComparison.OrdinalIgnoreCase))
                        {
                            filePath += ".txt";
                        }

                        File.WriteAllText(filePath, resultText);
                        statusLabel.Text = $"结果已保存到: {filePath}";
                        MessageBox.Show("数据导出成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"导出文件时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        statusLabel.Text = "导出失败";
                    }
                }
            }
        }

        private void LoadConfig()
        {
            try
            {
                if (File.Exists(configFilePath))
                {
                    var content = File.ReadAllText(configFilePath);
                    if (!string.IsNullOrEmpty(content))
                    {
                        satelliteList = new HashSet<string>(content.Split(','));
                        if (satListTextBox != null)
                        {
                            satListTextBox.Text = string.Join(Environment.NewLine, satelliteList.OrderBy(x => x));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveConfig()
        {
            try
            {
                var directory = Path.GetDirectoryName(configFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var satListStr = string.Join(",", satelliteList);
                File.WriteAllText(configFilePath, satListStr);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
