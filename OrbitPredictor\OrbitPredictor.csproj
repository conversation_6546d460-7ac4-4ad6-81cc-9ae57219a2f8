﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{AF6BF8C2-B74D-4FD2-8827-E3DAF37277BF}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>OrbitPredictor</RootNamespace>
    <AssemblyName>OrbitPredictor</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="GeoEngine, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\GeoEngine.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controls\ControlsHelper.cs" />
    <Compile Include="Controls\PanelEnhanced.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\TextBoxBottomBorder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\TimeInputBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TimeInputBox.Designer.cs">
      <DependentUpon>TimeInputBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DateTimeInputBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DateTimeInputBox.Designer.cs">
      <DependentUpon>DateTimeInputBox.cs</DependentUpon>
    </Compile>
    <Compile Include="OrbitDataView.cs" />
    <Compile Include="OrbitMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OrbitMain.Designer.cs">
      <DependentUpon>OrbitMain.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="SelectTleObject.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SelectTleObject.Designer.cs">
      <DependentUpon>SelectTleObject.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Controls\TimeInputBox.resx">
      <DependentUpon>TimeInputBox.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DateTimeInputBox.resx">
      <DependentUpon>DateTimeInputBox.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="OrbitMain.resx">
      <DependentUpon>OrbitMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="SelectTleObject.resx">
      <DependentUpon>SelectTleObject.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SignalAnalyzer.Common\SignalAnalyzer.Common.csproj">
      <Project>{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}</Project>
      <Name>SignalAnalyzer.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="favicon.ico" />
    <None Include="Resources\GDMap_05_A.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>