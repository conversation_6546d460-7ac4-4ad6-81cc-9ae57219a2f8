﻿using SignalAnalyzer.Utilities;
using System;
using System.Data;
using System.Data.SQLite;
using System.Windows.Forms;

namespace SignalAnalyzer
{
    public partial class RadarEdits : Form
    {
        public RadarEdits()
        {
            InitializeComponent();
        }

        private void RadarEdits_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (this.Owner != null && !this.Owner.IsDisposed)
            {
                this.Owner.Show();
            }
        }

        private void RadarEdits_Load(object sender, EventArgs e)
        {
            ReadRadarData();
            ReadSatelliteData();
        }

        private void ReadSatelliteData(bool isUpdate = false)
        {
            string sqlSat = "SELECT SatNo AS 卫星编号,SatName AS 卫星名称,SatEnName AS '卫星名称(En)'," +
                            "SatArea AS 所属国家,SatLaunchTime AS 发射时间,SatFreq AS 工作频段,SatNumbering AS 自编号 FROM DB_K_SATELLITE";
            DataSet setSat = SQLiteHelper.ExecuteDataSet(sqlSat);

            var index = this.satelliteData.CurrentRow?.Index;
            this.satelliteData.DataSource = setSat.Tables[0];

            // 设置列宽权重 - 响应式布局
            SetSatelliteDataColumnWeights();

            if (isUpdate)
            {
                if (index != null)
                {
                    this.satelliteData.Rows[index.Value].Selected = true;
                }
            }
        }

        private void SetSatelliteDataColumnWeights()
        {
            if (this.satelliteData.Columns.Count > 0)
            {
                // 设置各列的权重比例和最小宽度
                this.satelliteData.Columns["卫星编号"].FillWeight = 15;      // 较窄 - 编号通常较短
                this.satelliteData.Columns["卫星编号"].MinimumWidth = 80;

                this.satelliteData.Columns["卫星名称"].FillWeight = 25;      // 中等 - 中文名称
                this.satelliteData.Columns["卫星名称"].MinimumWidth = 120;

                this.satelliteData.Columns["卫星名称(En)"].FillWeight = 25;  // 中等 - 英文名称
                this.satelliteData.Columns["卫星名称(En)"].MinimumWidth = 120;

                this.satelliteData.Columns["所属国家"].FillWeight = 15;      // 较窄 - 国家名称较短
                this.satelliteData.Columns["所属国家"].MinimumWidth = 80;

                this.satelliteData.Columns["发射时间"].FillWeight = 20;      // 中等 - 日期格式
                this.satelliteData.Columns["发射时间"].MinimumWidth = 100;

                this.satelliteData.Columns["工作频段"].FillWeight = 15;      // 较窄 - 频段信息
                this.satelliteData.Columns["工作频段"].MinimumWidth = 80;

                this.satelliteData.Columns["自编号"].FillWeight = 15;        // 较窄 - 编号较短
                this.satelliteData.Columns["自编号"].MinimumWidth = 80;
            }
        }

        private void ReadRadarData(bool isUpdate = false)
        {
            //string sqlStr2 = "SELECT SatNo,SatName,RFTypeName,RFMax,RFMin,PRITypeName,PRIMax,PRIMin,PWTypeName,PWMax,PWMin FROM " +
            //    "DB_K_RADAR LEFT JOIN (DB_PT_RFT LEFT JOIN (DB_PT_PWT LEFT JOIN (DB_PT_PWT )))";
            string sqlRadar = "SELECT DB_K_RADAR.RID AS Rid, DB_K_RADAR.RadarNo AS 雷达编号, DB_K_RADAR.RadarFreq AS 雷达频段, " +
                            "DB_PT_RFT.ELENAME AS 射频类型, DB_K_RADAR.RFMin AS 射频最小值, DB_K_RADAR.RFMax AS 射频最大值, " +
                            "DB_PT_PRIT.ELENAME AS 重频类型, DB_K_RADAR.PRIMin AS 重复周期最小值, DB_K_RADAR.PRIMax AS 重复周期最大值, " +
                            "DB_PT_PWT.ELENAME AS 脉宽类型, DB_K_RADAR.PWMin AS 脉宽最小值, DB_K_RADAR.PWMax AS 脉宽最大值 " +
                            "FROM DB_PT_RFT RIGHT JOIN (DB_PT_PRIT RIGHT JOIN (DB_PT_PWT RIGHT JOIN DB_K_RADAR ON DB_PT_PWT.DECCODE = DB_K_RADAR.PWType) " +
                            "ON DB_PT_PRIT.DECCODE = DB_K_RADAR.PRIType) ON DB_PT_RFT.DECCODE = DB_K_RADAR.RFType ORDER BY DB_K_RADAR.RadarNo";
            DataSet setRadar = SQLiteHelper.ExecuteDataSet(sqlRadar);

            var index = this.satRadarTemplate.CurrentRow?.Index;
            this.satRadarTemplate.DataSource = setRadar.Tables[0];
            this.satRadarTemplate.Columns["Rid"].Visible = false;

            // 设置列宽权重 - 响应式布局
            SetRadarDataColumnWeights();

            if (isUpdate)
            {
                if (index != null)
                {
                    this.satRadarTemplate.Rows[index.Value].Selected = true;
                }
            }
        }

        private void SetRadarDataColumnWeights()
        {
            if (this.satRadarTemplate.Columns.Count > 0)
            {
                // 设置各列的权重比例和最小宽度 - 雷达参数表格
                this.satRadarTemplate.Columns["雷达编号"].FillWeight = 12;        // 较窄 - 编号
                this.satRadarTemplate.Columns["雷达编号"].MinimumWidth = 80;

                this.satRadarTemplate.Columns["雷达频段"].FillWeight = 12;        // 较窄 - 频段
                this.satRadarTemplate.Columns["雷达频段"].MinimumWidth = 80;

                this.satRadarTemplate.Columns["射频类型"].FillWeight = 10;        // 较窄 - 类型
                this.satRadarTemplate.Columns["射频类型"].MinimumWidth = 70;

                this.satRadarTemplate.Columns["射频最小值"].FillWeight = 8;       // 窄 - 数值
                this.satRadarTemplate.Columns["射频最小值"].MinimumWidth = 60;

                this.satRadarTemplate.Columns["射频最大值"].FillWeight = 8;       // 窄 - 数值
                this.satRadarTemplate.Columns["射频最大值"].MinimumWidth = 60;

                this.satRadarTemplate.Columns["重频类型"].FillWeight = 10;        // 较窄 - 类型
                this.satRadarTemplate.Columns["重频类型"].MinimumWidth = 70;

                this.satRadarTemplate.Columns["重复周期最小值"].FillWeight = 12;   // 中等 - 较长标题
                this.satRadarTemplate.Columns["重复周期最小值"].MinimumWidth = 90;

                this.satRadarTemplate.Columns["重复周期最大值"].FillWeight = 12;   // 中等 - 较长标题
                this.satRadarTemplate.Columns["重复周期最大值"].MinimumWidth = 90;

                this.satRadarTemplate.Columns["脉宽类型"].FillWeight = 10;        // 较窄 - 类型
                this.satRadarTemplate.Columns["脉宽类型"].MinimumWidth = 70;

                this.satRadarTemplate.Columns["脉宽最小值"].FillWeight = 8;       // 窄 - 数值
                this.satRadarTemplate.Columns["脉宽最小值"].MinimumWidth = 60;

                this.satRadarTemplate.Columns["脉宽最大值"].FillWeight = 8;       // 窄 - 数值
                this.satRadarTemplate.Columns["脉宽最大值"].MinimumWidth = 60;
            }
        }

        private void btnAddInfo_Click(object sender, EventArgs e)
        {
            AddInfo addInfo = new AddInfo();
            if (addInfo.ShowDialog() == DialogResult.OK)
            {
                ReadSatelliteData();
            }
        }
        
        private void btnEditInfo_Click(object sender, EventArgs e)
        {
            EditInfo editInfo = new EditInfo();
            editInfo.WhereIf = this.satelliteData.CurrentRow.Cells["卫星编号"].Value.ToString();
            if (editInfo.ShowDialog() == DialogResult.OK)
            {
                ReadSatelliteData(true);
                ReadRadarData();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //int row = SQLiteHelper.ExecuteNonQuery("create table if not exists test(name varchar(50), nno varchar(50))");
            //int res = SQLiteHelper.ExecuteNonQuery("insert into test(name, nno) values(\"ppp\", \"999\")");
            //DataSet set = SQLiteHelper.ExecuteDataSet("select * from test");
            //MessageBox.Show(row+"");
        }


        private void btnAddRadar_Click(object sender, EventArgs e)
        {
            SelectSatellite sSat = new SelectSatellite();
            sSat.SeletSatellite += (no, freq) =>
            {
                AddParam addParam = new AddParam(no, freq);
                if (addParam.ShowDialog() == DialogResult.OK)
                {
                    ReadRadarData();
                }
            };
            sSat.ShowDialog();
        }
        
        private void btnEditRadar_Click(object sender, EventArgs e)
        {
            EditParam editParam = new EditParam();
            editParam.WhereIf = this.satRadarTemplate.CurrentRow.Cells["雷达编号"].Value.ToString();
            editParam.WhereAnd = this.satRadarTemplate.CurrentRow.Cells["雷达频段"].Value.ToString();
            if (editParam.ShowDialog() == DialogResult.OK)
            {
                ReadRadarData(true);
            }
        }

        private void btnDelRadar_Click(object sender, EventArgs e)
        {
            var rid = this.satRadarTemplate.CurrentRow.Cells["Rid"].Value.ToString();
            int result = SQLiteHelper.ExecuteNonQuery($"DELETE FROM DB_K_RADAR WHERE RID=\"{ rid }\"");
            if (result > 0)
            {
                MessageBox.Show("删除成功");
                ReadRadarData();
            }
        }

        private void btnDelInfo_Click(object sender, EventArgs e)
        {
            var satNo = this.satelliteData.CurrentRow.Cells["卫星编号"].Value.ToString();
            if (MessageBox.Show($"确定删除编号为{ satNo }的信息吗？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information) == DialogResult.Yes)
            {
                int result = SQLiteHelper.ExecuteNonQuery($"DELETE FROM DB_K_SATELLITE WHERE SatNo=@SatNo", new SQLiteParameter[] {
                    new SQLiteParameter("@SatNo", satNo)
                });
                if (result > 0)
                {
                    SQLiteHelper.ExecuteNonQuery($"DELETE FROM DB_K_RADAR WHERE RadarNo=@RadarNo", new SQLiteParameter[] {
                        new SQLiteParameter("@RadarNo", satNo)
                    });
                    MessageBox.Show("删除成功");
                    ReadSatelliteData();
                    ReadRadarData();
                }
            }
        }
    }
}
